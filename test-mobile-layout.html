<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端布局测试</title>
    <style>
        /* 模拟WordPress主题的基本样式 */
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        /* 模拟archive--grid-two-layout的样式 */
        .archive--grid-two-layout .blogzee-inner-content-wrap {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
        }
        
        /* 移动端样式 */
        @media (max-width: 675px) {
            .archive--grid-two-layout .blogzee-inner-content-wrap {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px; /* 减小移动端间距 */
            }
        }
        
        /* 文章卡片样式 */
        .post-item {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .post-thumbnail {
            width: 100%;
            height: 120px;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 12px;
        }
        
        .post-content {
            padding: 15px;
        }
        
        /* 模拟修改后的post title样式 - 与description相同大小，无加粗，限制2行 */
        .post-title {
            font-size: 12px;
            font-weight: normal;
            margin: 0 0 8px 0;
            line-height: 1.4;
            /* 限制显示为2行 */
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .post-meta {
            font-size: 11px;
            color: #666;
            margin-bottom: 8px;
        }

        /* 显示comments数 */
        .post-comments-num {
            display: inline-block;
            margin-left: 8px;
            color: #666;
        }

        /* 隐藏post description */
        .post-excerpt {
            display: none;
        }
        
        .category-tag {
            background: #ff6b35;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            position: absolute;
            top: 8px;
            left: 8px;
        }
        
        .post-thumbnail {
            position: relative;
        }
    </style>
</head>
<body>
    <h1>移动端双排布局测试</h1>
    <p>请在移动设备或调整浏览器窗口宽度到675px以下查看效果</p>
    
    <div class="archive--grid-two-layout">
        <div class="blogzee-inner-content-wrap">
            <article class="post-item">
                <div class="post-thumbnail">
                    <span class="category-tag">Product Showcase</span>
                    图片区域
                </div>
                <div class="post-content">
                    <h3 class="post-title">Amazon Product Showcase #1: 61XABIVj9IL - This is a very long title that should be truncated to exactly two lines when displayed in the mobile layout</h3>
                    <div class="post-meta">Chieh Chu • 2 mins • <span class="post-comments-num">5 💬</span> • 100%</div>
                    <div class="post-excerpt">Showcasing Amazon product 61XABIVj9IL using direct external image URL embedding for optimal performance and zero storage overhead. This is a longer description that should be truncated to exactly two lines when displayed.</div>
                </div>
            </article>

            <article class="post-item">
                <div class="post-thumbnail">
                    <span class="category-tag">Product Showcase</span>
                    图片区域
                </div>
                <div class="post-content">
                    <h3 class="post-title">Amazon Product Showcase #2: 31Hr1XvHQSL - Another long title to test the two-line truncation feature</h3>
                    <div class="post-meta">Chieh Chu • 2 mins • <span class="post-comments-num">3 💬</span> • 75%</div>
                    <div class="post-excerpt">Showcasing Amazon product 31Hr1XvHQSL using direct external image URL embedding for optimal performance and zero storage overhead. This text is intentionally long to test the two-line truncation feature.</div>
                </div>
            </article>

            <article class="post-item">
                <div class="post-thumbnail">
                    <span class="category-tag">Product Showcase</span>
                    图片区域
                </div>
                <div class="post-content">
                    <h3 class="post-title">Amazon Product Showcase #3: 41EKhUc7TKL</h3>
                    <div class="post-meta">Admin • 2 mins • <span class="post-comments-num">8 💬</span> • 75%</div>
                    <div class="post-excerpt">Showcasing Amazon product 41EKhUc7TKL using direct external image URL embedding for optimal performance and zero storage overhead.</div>
                </div>
            </article>

            <article class="post-item">
                <div class="post-thumbnail">
                    <span class="category-tag">Product Showcase</span>
                    图片区域
                </div>
                <div class="post-content">
                    <h3 class="post-title">Amazon Product Showcase #4: Test Item with a Very Long Title That Should Demonstrate Text Truncation</h3>
                    <div class="post-meta">Chieh Chu • 2 mins • <span class="post-comments-num">12 💬</span> • 90%</div>
                    <div class="post-excerpt">This is another test item with a very long description that should demonstrate how the text truncation works when it exceeds two lines of content in the mobile layout.</div>
                </div>
            </article>
        </div>
    </div>
    
    <div style="margin-top: 30px; padding: 15px; background: #e8f4f8; border-radius: 8px;">
        <h3>测试说明：</h3>
        <ul>
            <li>✅ 移动端保持双排显示（而不是单排）</li>
            <li>✅ Post title字体大小与description相同（15px），取消加粗</li>
            <li>✅ Post title限制为2行，超出部分显示省略号</li>
            <li>✅ Post description完全隐藏，只显示title</li>
            <li>✅ 移除"continue reading"按钮</li>
            <li>✅ 移动端显示comments数（移除hide-on-mobile限制）</li>
            <li>✅ 移动端间距调整为15px（桌面端为30px）</li>
            <li>✅ 响应式设计在675px断点生效</li>
        </ul>
    </div>
</body>
</html>
