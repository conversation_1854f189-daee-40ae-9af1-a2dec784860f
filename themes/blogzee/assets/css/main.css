/**
1.0 Variables
2.0 Normalize
3.0 Header
4.0 Main Content Area
	4.1 Grid 
	4.2 List
5.0 Sidebar Settings
6.0 Widget Design
	6.1 Author Widget
	6.2 Category Widgets
	6.3 Grid Widgets
	6.4 List Widgets
	6.5 Social Share
	6.6 Tag Collection
7.0 Pagination & ajax button
8.0 Post Format
9.0 Single Posts
10.0 Category, Tags Author page
11.0 Search Popup
12.0 Single Page
13.0 Main Banner
14.0 Carousel
15.0 Breadcrumb
16.0 Canvash Section
17.0 Scroll To Top
18.0 Footer
19.0 Advertisement
20.0 Swiper Slider
21.0 Hover effect and animation
22.0 Video Playlist
23.0 Live search
25.0 Category Collection
26.0 Table Of Content
27.0 Infinite Scroll

**/

/*
=========================================
1.0 Variables
=========================================
*/

:root {

	/* presents */
		--blogzee-global-preset-color-1 : #64748b;
		--blogzee-global-preset-color-2 : #27272a;
		--blogzee-global-preset-color-3 : #ef4444;
		--blogzee-global-preset-color-4 : #eab308;
		--blogzee-global-preset-color-5 : #84cc16;
		--blogzee-global-preset-color-6 : #22c55e;
		--blogzee-global-preset-color-7 : #06b6d4;
		--blogzee-global-preset-color-8 : #0284c7;
		--blogzee-global-preset-color-9 : #6366f1;
		--blogzee-global-preset-color-10 : #84cc16;
		--blogzee-global-preset-color-11 : #a855f7;
		--blogzee-global-preset-color-12 : #f43f5e;
		--blogzee-global-preset-gradient-1 : linear-gradient( 135deg, #485563 10%, #29323c 100%);
		--blogzee-global-preset-gradient-2 : linear-gradient( 135deg, #FF512F 10%, #F09819 100%);
		--blogzee-global-preset-gradient-3 : linear-gradient( 135deg, #00416A 10%, #E4E5E6 100%);
		--blogzee-global-preset-gradient-4 : linear-gradient( 135deg, #CE9FFC 10%, #7367F0 100%);
		--blogzee-global-preset-gradient-5 : linear-gradient( 135deg, #90F7EC 10%, #32CCBC 100%);
		--blogzee-global-preset-gradient-6 : linear-gradient( 135deg, #81FBB8 10%, #28C76F 100%);
		--blogzee-global-preset-gradient-7 : linear-gradient( 135deg, #EB3349 10%, #F45C43 100%);
		--blogzee-global-preset-gradient-8 : linear-gradient( 135deg, #FFF720 10%, #3CD500 100%);
		--blogzee-global-preset-gradient-9 : linear-gradient( 135deg, #FF96F9 10%, #C32BAC 100%);
		--blogzee-global-preset-gradient-10 : linear-gradient( 135deg, #69FF97 10%, #00E4FF 100%);
		--blogzee-global-preset-gradient-11 : linear-gradient( 135deg, #3C8CE7 10%, #00EAFF 100%);
		--blogzee-global-preset-gradient-12 : linear-gradient( 135deg, #FF7AF5 10%, #513162 100%);

		--blogzee-white-dark-color: #fff;
		--blogzee-black-dark-color: #2f2e2e;

	/* theme color */
		--blogzee-global-preset-theme-color: #2f2e2e;
		--blogzee-global-preset-gradient-theme-color: linear-gradient(130deg, #942cddcc 0%, #38a3e2cc 100%);
		--blogzee-footer-widget-border-color: #6565652b;
		--blogzee-border-dark-color: #d9d9d9;
		--blogzee-white-bk: #fff;
		--blogzee-white-text: #fff;
		--blogzee-site-preloader-bk-color: #040404eb;
		--blogzee-archive-text-color: #000;

	/* Site Background Color */
		--blogzee-site-bk-color: linear-gradient(130deg, #682BD4 0%, #19CEAC 100%);

	/* Site title */
		--blogzee-site-title-size: 38px;
		--blogzee-site-title-size-tab: 32px;
		--blogzee-site-title-size-mobile: 30px;
		--blogzee-site-title-lineheight: 45px;
		--blogzee-site-title-lineheight-tab: 42px;
		--blogzee-site-title-lineheight-mobile: 40px;
		--blogzee-site-title-letterspacing: 0;
		--blogzee-site-title-letterspacing-tab: 0;
		--blogzee-site-title-letterspacing-mobile: 0;
		--blogzee-site-title-textdecoration: none;
		--blogzee-site-title-texttransform: none;
		--blogzee-site-title-weight: 700;
		--blogzee-site-title-family: "Unna",sans-serif;
		--blogzee-site-title-style: italic;

	/* Site description */
		--blogzee-site-description-size: 15px;
		--blogzee-site-description-size-tab: 15px;
		--blogzee-site-description-size-mobile: 15px;
		--blogzee-site-description-lineheight: 22px;
		--blogzee-site-description-lineheight-tab: 22px;
		--blogzee-site-description-lineheight-mobile: 20px;
		--blogzee-site-description-letterspacing: 0;
		--blogzee-site-description-letterspacing-tab: 0;
		--blogzee-site-description-letterspacing-mobile: 0;
		--blogzee-site-description-textdecoration: none;
		--blogzee-site-description-texttransform: none;
		--blogzee-site-description-weight: 400;
		--blogzee-site-description-family: "Poppins",sans-serif;
		--blogzee-site-description-style: italic;

	/* Scroll To Top */
		--blogzee-scroll-text-color: #fff;
		--blogzee-scroll-text-color-hover: #fff;
		--blogzee-scroll-bk-color: #333;
		--blogzee-scroll-bk-color-hover: #666;

	/** Menu Font */
		--blogzee-menu-size: 16px;
		--blogzee-menu-size-tab: 15px;
		--blogzee-menu-size-mobile: 15px;
		--blogzee-menu-family: 'Outfit', sans-serif;
		--blogzee-menu-font-style: normal;
		--blogzee-menu-lineheight: 26px;
		--blogzee-menu-lineheight-tab: 24px;
		--blogzee-menu-lineheight-mobile: 24px;
		--blogzee-menu-letterspacing: 0.3px;
		--blogzee-menu-letterspacing-tab: 0.3px;
		--blogzee-menu-letterspacing-mobile: 0.3px;
		--blogzee-menu-textdecoration: 'none';
		--blogzee-menu-texttransform: 'none';
		--blogzee-menu-color: #fff;
		--blogzee-menu-color-hover: #fff;
		--blogzee-menu-weight: 500;
		--blogzee-menu-color-active: #FF376C;
		--blogzee-menu-color-submenu: #333333;
		--blogzee-menu-color-submenu-hover: #222222;
		--blogzee-submenu-bk-color: #ffffff;

	/* Submenu */
		--blogzee-submenu-size: 14px;
		--blogzee-submenu-size-tab: 14px;
		--blogzee-submenu-size-mobile: 16px;
		--blogzee-submenu-family: 'Outfit', sans-serif;
		--blogzee-submenu-lineheight: 26px;
		--blogzee-submenu-lineheight-tab: 24px;
		--blogzee-submenu-lineheight-mobile: 24px;
		--blogzee-submenu-weight: 400;
		--blogzee-submenu-letterspacing: 0.3;
		--blogzee-submenu-letterspacing-tab: 0.3;
		--blogzee-submenu-letterspacing-mobile: 0.3;
		--blogzee-submenu-textdecoration: 'none';
		--blogzee-submenu-texttransform: 'none';
		--blogzee-submenu-background-color: #cfa9e022;
		--blogzee-submenu-font-family: "Poppins",sans-serif;
		--blogzee-submenu-font-size: 16px;
		--blogzee-submenu-font-color: #333;
		--blogzee-submenu-font-weight: 500;
		--blogzee-submenu-font-style: normal;

	/** Footer Menu Font */
		--blogzee-footer-menu-size: 16px;
		--blogzee-footer-menu-size-tab: 15px;
		--blogzee-footer-menu-size-mobile: 15px;
		--blogzee-footer-menu-family: 'Outfit', sans-serif;
		--blogzee-footer-menu-font-style: normal;
		--blogzee-footer-menu-lineheight: 26px;
		--blogzee-footer-menu-lineheight-tab: 24px;
		--blogzee-footer-menu-lineheight-mobile: 24px;
		--blogzee-footer-menu-letterspacing: 0.3px;
		--blogzee-footer-menu-letterspacing-tab: 0.3px;
		--blogzee-footer-menu-letterspacing-mobile: 0.3px;
		--blogzee-footer-menu-textdecoration: 'none';
		--blogzee-footer-menu-texttransform: 'none';
		--blogzee-footer-menu-weight: 500;
		--blogzee-footer-menu-color: #000;
		--blogzee-footer-menu-color-hover: #000;
		
	/* custom menu button font */
		--blogzee-custom-button-family: "Poppins",sans-serif;
		--blogzee-custom-button-color: #fff;
		--blogzee-custom-button-color-hover: #fff;
		--blogzee-custom-button-icon-color: #fff;
		--blogzee-custom-button-icon-color-hover: #fff;
		--blogzee-custom-button-size: 15px;
		--blogzee-custom-button-size-tab: 15px;
		--blogzee-custom-button-size-mobile: 15px;
		--blogzee-custom-button-lineheight: 26px;
		--blogzee-custom-button-lineheight-tab: 24px;
		--blogzee-custom-button-lineheight-mobile: 24px;
		--blogzee-custom-button-letterspacing: 0.3px;
		--blogzee-custom-button-letterspacing-tab: 0.3px;
		--blogzee-custom-button-letterspacing-mobile: 0.3px;
		--blogzee-custom-button-weight: 400;
		--blogzee-custom-button-textdecoration: 'none';
		--blogzee-custom-button-texttransform: 'none';
		--blogzee-custom-button-bk-color: #FF376C;
		--blogzee-custom-button-bk-color-hover: #FF376C;
		--blogzee-custom-button-border-radius: 40px;
		--blogzee-custom-button-border-radius-tab: 40px;
		--blogzee-custom-button-border-radius-mobile: 40px;
		--blogzee-custom-button-style: italic;

	/* Theme mode */
		--blogzee-theme-mode-color: #ffffff;
		--blogzee-theme-mode-color-hover: #ffffff;
		--blogzee-theme-darkmode-color: #ffffff;
		--blogzee-theme-darkmode-color-hover: #ffffff;

	/* search header color */
		--blogzee-search-icon-color: #ffffff;
		--blogzee-search-icon-color-hover: #ffffff;
		--blogzee-search-viewall-color: #ffffff;
		--blogzee-search-viewall-color-hover: #ffffff;
		--blogzee-search-viewall-bkcolor: #FF376C;
		--blogzee-search-viewall-bkcolor-hover: #FF376C;

	/* Canvas Menu */
		--blogzee-canvas-menu-width: 370px;
		--blogzee-canvas-content-bk-color: #f0f0f0;
		--blogzee-canvas-icon-color: #ffffff;
		--blogzee-canvas-icon-color-hover: #ffffff;
		--blogzee-mobile-canvas-icon-color: #2f338d;
		--blogzee-mobile-canvas-icon-color-hover: #2f338d;
		
		/* responsive canvas */
		--blogzee-canvas-color: #000;

	/** Pagination color */
		--blogzee-archive-pagination-color: #2f2e2e;
		--blogzee-archive-pagination-bk-color: #fff;
		--blogzee-ajax-pagination-color: #2f2e2e;
		--blogzee-ajax-pagination-bk-color: #fff;
		--blogzee-ajax-pagination-color-hover: #ffffff;
		--blogzee-ajax-pagination-bk-color-hover: #FF376C;

	/* Block Title font */
		--blogzee-title-font-family: "Montserrat",sans-serif;
		--blogzee-title-font-style: normal;
		--blogzee-title-font-size: 22px;
		--blogzee-title-font-color: #222;
		--blogzee-title-font-weight: 600;

	/* Meta font */
		--blogzee-meta-font-family: "Montserrat",sans-serif;
		--blogzee-meta-font-size: 14px;
		--blogzee-meta-font-color: #000;
		--blogzee-meta-font-weight: 500;
		--blogzee-meta-icon-color: #FF376C;
		--blogzee-meta-font-style: normal;
		
	/* Archive image ratio */
		--blogzee-archive-post-image-ratio: 0.65;
		--blogzee-archive-post-image-ratio-tab: 0.65;
		--blogzee-archive-post-image-ratio-mobile: 0.75;

	/** article box shadow */
		--blogzee-article-box-shadow: 0 3px 12px -1px rgba(7,10,25,0.05), 0 22px 27px -20px rgba(7,10,25,0.05);
		--blogzee-image-box-shadow: 2px 2px 5px 1px rgba(0,0,0,0.25);

	/** Move to top **/
		--blogzee-scroll-top-icontext-color: #ffffff;
		--blogzee-scroll-top-icontext-color-hover: #ffffff;
		--blogzee-scroll-top-bk-color: #333333;
		--blogzee-scroll-top-bk-color-hover: #888888;

	/** Main Banner **/
		--blogzee-banner-title-font-size: 20px;
		--blogzee-banner-title-font-size-tab: 20px;
		--blogzee-banner-title-font-size-mobile: 20px;
		--blogzee-banner-title-font-lineheight: 33px;
		--blogzee-banner-title-font-lineheight-tab: 33px;
		--blogzee-banner-title-font-lineheight-mobile: 33px;
		--blogzee-banner-title-font-letterspacing: 0.6px;
		--blogzee-banner-title-font-letterspacing-tab: 0.6px;
		--blogzee-banner-title-font-letterspacing-mobile: 0.6px;
		--blogzee-banner-title-font-textdecoration: none;
		--blogzee-banner-title-font-texttransform: none;
		--blogzee-banner-title-font-weight: 400;
		--blogzee-banner-title-font-family: "Montserrat",sans-serif;
		--blogzee-banner-title-font-style: normal;
		--blogzee-banner-title-color: #000;
		--blogzee-banner-meta-color: #000;
		--blogzee-banner-excerpt-color: #000;
		--blogzee-banner-scrollbar-color: #000;

		/** Main Banner Sidebar Post Title **/
		--blogzee-banner-sidebar-title-font-size: 15px;
		--blogzee-banner-sidebar-title-font-size-tab: 15px;
		--blogzee-banner-sidebar-title-font-size-mobile: 15px;
		--blogzee-banner-sidebar-title-font-lineheight: 22px;
		--blogzee-banner-sidebar-title-font-lineheight-tab: 22px;
		--blogzee-banner-sidebar-title-font-lineheight-mobile: 22px;
		--blogzee-banner-sidebar-title-font-letterspacing: 0;
		--blogzee-banner-sidebar-title-font-letterspacing-tab: 0;
		--blogzee-banner-sidebar-title-font-letterspacing-mobile: 0;
		--blogzee-banner-sidebar-title-font-textdecoration: none;
		--blogzee-banner-sidebar-title-font-texttransform: none;
		--blogzee-banner-sidebar-title-font-weight: 600;
		--blogzee-banner-sidebar-title-font-family: "Montserrat",sans-serif;
		--blogzee-banner-sidebar-title-font-style: normal;

		/** Main Banner Sidebar Block Title **/
		--blogzee-banner-sidebar-block-font-size: 24px;
		--blogzee-banner-sidebar-block-font-size-tab: 24px;
		--blogzee-banner-sidebar-block-font-size-mobile: 24px;
		--blogzee-banner-sidebar-block-font-lineheight: 26px;
		--blogzee-banner-sidebar-block-font-lineheight-tab: 26px;
		--blogzee-banner-sidebar-block-font-lineheight-mobile: 26px;
		--blogzee-banner-sidebar-block-font-letterspacing: 0;
		--blogzee-banner-sidebar-block-font-letterspacing-tab: 0;
		--blogzee-banner-sidebar-block-font-letterspacing-mobile: 0;
		--blogzee-banner-sidebar-block-font-textdecoration: none;
		--blogzee-banner-sidebar-block-font-texttransform: none;
		--blogzee-banner-sidebar-block-font-weight: 600;
		--blogzee-banner-sidebar-block-font-family: "Montserrat",sans-serif;
		--blogzee-banner-sidebar-block-font-style: normal;

		/* main banner excerpt */
		--blogzee-banner-excerpt-font-family: "Poppins",sans-serif;
		--blogzee-banner-excerpt-font-size: 14px;
		--blogzee-banner-excerpt-font-size-tab: 14px;
		--blogzee-banner-excerpt-font-size-mobile: 14px;
		--blogzee-banner-excerpt-font-lineheight: 25;
		--blogzee-banner-excerpt-font-lineheight-tab: 25;
		--blogzee-banner-excerpt-font-lineheight-mobile: 25;
		--blogzee-banner-excerpt-font-letterspacing: 0;
		--blogzee-banner-excerpt-font-letterspacing-tab: 0;
		--blogzee-banner-excerpt-font-letterspacing-mobile: 0;
		--blogzee-banner-excerpt-font-textdecoration: none;
		--blogzee-banner-excerpt-font-texttransform: unset;
		--blogzee-banner-excerpt-font-weight: 400;
		--blogzee-banner-excerpt-font-style: normal;

	/* Header */
		--blogzee-header-social-color: #000;
		--blogzee-header-social-color-hover: #000;

	/* Header */
		--blogzee-footer-social-color: #000;
		--blogzee-footer-social-color-hover: #000;

	/* Background Animation Color */
		--blogzee-animation-object-color: #3858F6;

	/** Archive Typography **/
		/* post title */
			--blogzee-post-title-font-size: 22px;
			--blogzee-post-title-font-size-tab: 20px;
			--blogzee-post-title-font-size-mobile: 20px;
			--blogzee-post-title-font-lineheight: 30px;
			--blogzee-post-title-font-lineheight-tab: 30px;
			--blogzee-post-title-font-lineheight-mobile: 30px;
			--blogzee-post-title-font-letterspacing: 0;
			--blogzee-post-title-font-letterspacing-tab: 0;
			--blogzee-post-title-font-letterspacing-mobile: 0;
			--blogzee-post-title-font-family: "Lora",sans-serif;
			--blogzee-post-title-font-style: normal;
			--blogzee-post-title-font-color: #111827;
			--blogzee-post-title-font-weight: 600;
			--blogzee-post-title-font-texttransform: 'none';
			--blogzee-post-title-font-textdecoration: 'none';

		/* post exceprt */
			--blogzee-post-content-font-size: 15px;
			--blogzee-post-content-font-size-tab: 15px;
			--blogzee-post-content-font-size-mobile: 15px;
			--blogzee-post-content-font-lineheight: 25px;
			--blogzee-post-content-font-lineheight-tab: 25px;
			--blogzee-post-content-font-lineheight-mobile: 25px;
			--blogzee-post-content-font-letterspacing: 0.3px;
			--blogzee-post-content-font-letterspacing-tab: 0.3px;
			--blogzee-post-content-font-letterspacing-mobile: 0.3px;
			--blogzee-post-content-font-family: "Poppins",sans-serif;
			--blogzee-post-content-font-style: normal;
			--blogzee-post-content-font-color: #000;
			--blogzee-post-content-font-weight: 400;
			--blogzee-post-content-font-texttransform: 'none';
			--blogzee-post-content-font-textdecoration: 'none';

		/* Category font */			
			--blogzee-category-font-size: 15px;
			--blogzee-category-font-size-tab: 15px;
			--blogzee-category-font-size-mobile: 15px;
			--blogzee-category-font-lineheight: 22px;
			--blogzee-category-font-lineheight-tab: 22px;
			--blogzee-category-font-lineheight-mobile: 22px;
			--blogzee-category-font-letterspacing: 0.5px;
			--blogzee-category-font-letterspacing-tab: 0.5px;
			--blogzee-category-font-letterspacing-mobile: 0.5px;
			--blogzee-category-font-family: "Montserrat",sans-serif;
			--blogzee-category-font-style: normal;
			--blogzee-category-font-color: #fff;
			--blogzee-category-font-weight: 700;
			--blogzee-category-icon-color: #FF376C;
			--blogzee-category-bk-color: #c580e3;
			--blogzee-category-font-texttransform: 'Capitalize';
			--blogzee-category-font-textdecoration: 'none';

		/* Date font */
			--blogzee-date-font-size: 14px;
			--blogzee-date-font-size-tab: 14px;
			--blogzee-date-font-size-mobile: 14px;
			--blogzee-date-font-lineheight: 20px;
			--blogzee-date-font-lineheight-tab: 20px;
			--blogzee-date-font-lineheight-mobile: 20px;
			--blogzee-date-font-letterspacing: 0.3px;
			--blogzee-date-font-letterspacing-tab: 0.3px;
			--blogzee-date-font-letterspacing-mobile: 0.3px;
			--blogzee-date-font-family: "Montserrat",sans-serif;
			--blogzee-date-font-color: #333;
			--blogzee-date-font-weight: 500;
			--blogzee-date-font-texttransform: 'none';
			--blogzee-date-font-textdecoration: 'none';
			--blogzee-date-icon-color: #FF376C;
			--blogzee-date-font-style: normal;

		/* Author font */
			--blogzee-author-font-size: 14px;
			--blogzee-author-font-size-tab: 14px;
			--blogzee-author-font-size-mobile: 14px;
			--blogzee-author-font-lineheight: 20px;
			--blogzee-author-font-lineheight-tab: 20px;
			--blogzee-author-font-lineheight-mobile: 20px;
			--blogzee-author-font-letterspacing: 0.3px;
			--blogzee-author-font-letterspacing-tab: 0.3px;
			--blogzee-author-font-letterspacing-mobile: 0.3px;
			--blogzee-author-font-family: "Montserrat",sans-serif;
			--blogzee-author-font-color: #333;
			--blogzee-author-font-weight: 500;
			--blogzee-author-font-texttransform: 'Capitalize';
			--blogzee-author-font-textdecoration: 'none';
			--blogzee-author-font-style: normal;

		/* Read Time font */
			--blogzee-readtime-font-size: 14px;
			--blogzee-readtime-font-size-tab: 14px;
			--blogzee-readtime-font-size-mobile: 14px;
			--blogzee-readtime-font-lineheight: 20px;
			--blogzee-readtime-font-lineheight-tab: 20px;
			--blogzee-readtime-font-lineheight-mobile: 20px;
			--blogzee-readtime-font-letterspacing: 0.3px;
			--blogzee-readtime-font-letterspacing-tab: 0.3px;
			--blogzee-readtime-font-letterspacing-mobile: 0.3px;
			--blogzee-readtime-font-family: "Montserrat",sans-serif;
			--blogzee-readtime-font-color: #333;
			--blogzee-readtime-font-weight: 500;
			--blogzee-readtime-font-textdecoration: none;
			--blogzee-readtime-font-texttransform: none;
			--blogzee-readtime-font-style: normal;

		/* Read More font */
			--blogzee-readmore-font-size: 13px;
			--blogzee-readmore-font-size-tab: 13px;
			--blogzee-readmore-font-size-mobile: 13px;
			--blogzee-readmore-font-lineheight: 20px;
			--blogzee-readmore-font-lineheight-tab: 20px;
			--blogzee-readmore-font-lineheight-mobile: 20px;
			--blogzee-readmore-font-letterspacing: 0.3px;
			--blogzee-readmore-font-letterspacing-tab: 0.3px;
			--blogzee-readmore-font-letterspacing-mobile: 0.3px;
			--blogzee-readmore-font-family: "Poppins",sans-serif;
			--blogzee-readmore-font-color: #3858f6;
			--blogzee-readmore-font-color-hover: #3858f6;
			--blogzee-readmore-bk-color: #fff;
			--blogzee-readmore-bk-color-hover: #fff;
			--blogzee-readmore-font-weight: 500;
			--blogzee-readmore-font-textdecoration: none;
			--blogzee-readmore-font-texttransform: none;
			--blogzee-readmore-font-style: normal;

		/* Comment font */
			--blogzee-comment-font-size: 14px;
			--blogzee-comment-font-size-tab: 14px;
			--blogzee-comment-font-size-mobile: 14px;
			--blogzee-comment-font-lineheight: 20px;
			--blogzee-comment-font-lineheight-tab: 20px;
			--blogzee-comment-font-lineheight-mobile: 20px;
			--blogzee-comment-font-letterspacing: 0.3px;
			--blogzee-comment-font-letterspacing-tab: 0.3px;
			--blogzee-comment-font-letterspacing-mobile: 0.3px;
			--blogzee-comment-font-family: "Montserrat",sans-serif;
			--blogzee-comment-font-color: #333;
			--blogzee-comment-font-weight: 500;
			--blogzee-comment-font-textdecoration: none;
			--blogzee-comment-font-texttransform: none;
			--blogzee-comment-font-style: normal;
		--blogzee-main-font-family: "Montserrat",sans-serif;

	/* Category Collection font */
		--blogzee-category-collection-font-family: "Poppins, sans-serif";
		--blogzee-category-collection-font-size: 13px;
		--blogzee-category-collection-font-size-tab: 13px;
		--blogzee-category-collection-font-size-mobile: 13px;
		--blogzee-category-collection-font-lineheight: 17px;
		--blogzee-category-collection-font-lineheight-tab: 17px;
		--blogzee-category-collection-font-line-height-mobile: 17px;
		--blogzee-category-collection-font-letterspacing: 0.3px;
		--blogzee-category-collection-font-letterspacing-tab: 0.3px;
		--blogzee-category-collection-font-letterspacing-mobile: 0.3px;
		--blogzee-category-collection-font-weight: 500;
		--blogzee-category-collection-font-textdecoration: none;
		--blogzee-category-collection-font-texttransform: none;
		--blogzee-category-collection-font-style: normal;

	/* You May Have Missed */
		/* Block Title */
			--blogzee-youmaymissed-block-title-font-family: "Unna, sans-serif";
			--blogzee-youmaymissed-block-title-font-size: 24px;
			--blogzee-youmaymissed-block-title-font-size-tab: 24px;
			--blogzee-youmaymissed-block-title-font-size-mobile: 24px;
			--blogzee-youmaymissed-block-title-font-lineheight: 36px;
			--blogzee-youmaymissed-block-title-font-lineheight-tab: 36px;
			--blogzee-youmaymissed-block-title-font-line-height-mobile: 36px;
			--blogzee-youmaymissed-block-title-font-letterspacing: 0.3px;
			--blogzee-youmaymissed-block-title-font-letterspacing-tab: 0.3px;
			--blogzee-youmaymissed-block-title-font-letterspacing-mobile: 0.3px;
			--blogzee-youmaymissed-block-title-font-weight: 700;
			--blogzee-youmaymissed-block-title-font-textdecoration: none;
			--blogzee-youmaymissed-block-title-font-texttransform: none;
			--blogzee-youmaymissed-block-title-font-style: normal;
			--blogzee-youmaymissed-block-title-color: #000;
			--youmaymissed-section-bk-color: #fff;

		/* Post Title */
			--blogzee-youmaymissed-title-font-family: "Unna, sans-serif";
			--blogzee-youmaymissed-title-font-size: 13px;
			--blogzee-youmaymissed-title-font-size-tab: 13px;
			--blogzee-youmaymissed-title-font-size-mobile: 13px;
			--blogzee-youmaymissed-title-font-lineheight: 17px;
			--blogzee-youmaymissed-title-font-lineheight-tab: 17px;
			--blogzee-youmaymissed-title-font-line-height-mobile: 17px;
			--blogzee-youmaymissed-title-font-letterspacing: 0.3px;
			--blogzee-youmaymissed-title-font-letterspacing-tab: 0.3px;
			--blogzee-youmaymissed-title-font-letterspacing-mobile: 0.3px;
			--blogzee-youmaymissed-title-font-weight: 700;
			--blogzee-youmaymissed-title-font-textdecoration: none;
			--blogzee-youmaymissed-title-font-texttransform: none;
			--blogzee-youmaymissed-title-font-style: italic;

		/* Category */
			--blogzee-youmaymissed-category-font-size: 11px;
			--blogzee-youmaymissed-category-font-size-tab: 11px;
			--blogzee-youmaymissed-category-font-size-mobile: 11px;
			--blogzee-youmaymissed-category-font-lineheight: 22px;
			--blogzee-youmaymissed-category-font-lineheight-tab: 22px;
			--blogzee-youmaymissed-category-font-lineheight-mobile: 22px;
			--blogzee-youmaymissed-category-font-letterspacing: 0.5px;
			--blogzee-youmaymissed-category-font-letterspacing-tab: 0.5px;
			--blogzee-youmaymissed-category-font-letterspacing-mobile: 0.5px;
			--blogzee-youmaymissed-category-font-family: "Poppins",sans-serif;
			--blogzee-youmaymissed-category-font-style: normal;
			--blogzee-youmaymissed-category-font-color: #fff;
			--blogzee-youmaymissed-category-font-weight: 400;
			--blogzee-youmaymissed-category-font-texttransform: 'Capitalize';
			--blogzee-youmaymissed-category-font-textdecoration: 'none';

		/* Date */
			--blogzee-youmaymissed-date-font-size: 11px;
			--blogzee-youmaymissed-date-font-size-tab: 11px;
			--blogzee-youmaymissed-date-font-size-mobile: 11px;
			--blogzee-youmaymissed-date-font-lineheight: 20px;
			--blogzee-youmaymissed-date-font-lineheight-tab: 20px;
			--blogzee-youmaymissed-date-font-lineheight-mobile: 20px;
			--blogzee-youmaymissed-date-font-letterspacing: 0.3px;
			--blogzee-youmaymissed-date-font-letterspacing-tab: 0.3px;
			--blogzee-youmaymissed-date-font-letterspacing-mobile: 0.3px;
			--blogzee-youmaymissed-date-font-family: "Poppins",sans-serif;
			--blogzee-youmaymissed-date-font-weight: 500;
			--blogzee-youmaymissed-date-font-texttransform: 'none';
			--blogzee-youmaymissed-date-font-textdecoration: 'none';
			--blogzee-youmaymissed-date-font-style: normal;

		/* Author font */
			--blogzee-youmaymissed-author-font-size: 11px;
			--blogzee-youmaymissed-author-font-size-tab: 11px;
			--blogzee-youmaymissed-author-font-size-mobile: 11px;
			--blogzee-youmaymissed-author-font-lineheight: 22px;
			--blogzee-youmaymissed-author-font-lineheight-tab: 22px;
			--blogzee-youmaymissed-author-font-lineheight-mobile: 22px;
			--blogzee-youmaymissed-author-font-letterspacing: 0.3px;
			--blogzee-youmaymissed-author-font-letterspacing-tab: 0.3px;
			--blogzee-youmaymissed-author-font-letterspacing-mobile: 0.3px;
			--blogzee-youmaymissed-author-font-family: "Poppins",sans-serif;
			--blogzee-youmaymissed-author-font-weight: 500;
			--blogzee-youmaymissed-author-font-texttransform: 'Capitalize';
			--blogzee-youmaymissed-author-font-textdecoration: 'none';
			--blogzee-youmaymissed-author-font-style: normal;
		--blogzee-youmaymissed-image-ratio: 1;

		/* you may missedimage ratio */
			--blogzee-youmaymissed-image-ratio: 0.6;
			--blogzee-youmaymissed-image-ratio-tab: 0.6;
			--blogzee-youmaymissed-image-ratio-mobile: 0.6;

	/** widget font family **/
		/** Widget Block **/
			--blogzee-widget-block-font-size: 24px;
			--blogzee-widget-block-font-size-tab: 24px;
			--blogzee-widget-block-font-size-mobile: 24px;
			--blogzee-widget-block-font-lineheight: 36px;
			--blogzee-widget-block-font-lineheight-tab: 36px;
			--blogzee-widget-block-font-lineheight-mobile: 36px;
			--blogzee-widget-block-font-letterspacing: 0.6px;
			--blogzee-widget-block-font-letterspacing-tab: 0.6px;
			--blogzee-widget-block-font-letterspacing-mobile: 0.6px;
			--blogzee-widget-block-font-family: "Gelasio",sans-serif;
			--blogzee-widget-block-font-texttransform: unset;
			--blogzee-widget-block-font-textdecoration: none;
			--blogzee-widget-block-font-color: #222;
			--blogzee-widget-block-font-weight: 600;
			--blogzee-widget-block-font-style: normal;

		/** Widget Post title Block **/
			--blogzee-widget-title-font-size: 17px;
			--blogzee-widget-title-font-size-tab: 17px;
			--blogzee-widget-title-font-size-mobile: 17px;
			--blogzee-widget-title-font-lineheight: 25px;
			--blogzee-widget-title-font-lineheight-tab: 25px;
			--blogzee-widget-title-font-lineheight-mobile: 25px;
			--blogzee-widget-title-font-letterspacing: 0.3px;
			--blogzee-widget-title-font-letterspacing-tab: 0.3px;
			--blogzee-widget-title-font-letterspacing-mobile: 0.3px;
			--blogzee-widget-title-font-family: "Lora",sans-serif;
			--blogzee-widget-title-font-texttransform: unset;
			--blogzee-widget-title-font-textdecoration: none;
			--blogzee-widget-title-font-color: #222;
			--blogzee-widget-title-font-weight: 500;
			--blogzee-widget-title-font-style: normal;

		/** Widget Post date **/
			--blogzee-widget-date-font-size: 12px;
			--blogzee-widget-date-font-size-tab: 12px;
			--blogzee-widget-date-font-size-mobile: 12px;
			--blogzee-widget-date-font-lineheight: 20px;
			--blogzee-widget-date-font-lineheight-tab: 20px;
			--blogzee-widget-date-font-lineheight-mobile: 20px;
			--blogzee-widget-date-font-letterspacing: 0.3px;
			--blogzee-widget-date-font-letterspacing-tab: 0.3px;
			--blogzee-widget-date-font-letterspacing-mobile: 0.3px;
			--blogzee-widget-date-font-family: "Poppins",sans-serif;
			--blogzee-widget-date-font-texttransform: unset;
			--blogzee-widget-date-font-textdecoration: none;
			--blogzee-widget-date-font-color: #222;
			--blogzee-widget-date-font-weight: 400;
			--blogzee-widget-date-font-style: normal;

		/** Widget Post category **/
			--blogzee-widget-category-font-size: 14px;
			--blogzee-widget-category-font-size-tab: 14px;
			--blogzee-widget-category-font-size-mobile: 14px;
			--blogzee-widget-category-font-lineheight: 20px;
			--blogzee-widget-category-font-lineheight-tab: 20px;
			--blogzee-widget-category-font-lineheight-mobile: 20px;
			--blogzee-widget-category-font-letterspacing: 0.5px;
			--blogzee-widget-category-font-letterspacing-tab: 0.5px;
			--blogzee-widget-category-font-letterspacing-mobile: 0.5px;
			--blogzee-widget-category-font-family: "Poppins",sans-serif;
			--blogzee-widget-category-font-style: normal;
			--blogzee-widget-category-font-color: #222;
			--blogzee-widget-category-font-weight: 400;
			--blogzee-widget-category-font-texttransform: 'capitalize';
			--blogzee-widget-category-font-textdecoration: 'none';
			--blogzee-widget-content-font-family: "Poppins",sans-serif;
			--blogzee-widget-content-font-style: normal;
	
	/** Single Typography **/
		--blogzee-single-post-image-ratio: 0.55;
		--blogzee-single-post-image-ratio-tab: 0.55;
		--blogzee-single-post-image-ratio-mobile: 0.55;
		--blogzee-single-page-image-ratio: 0.55;
		--blogzee-single-page-image-ratio-tab: 0.55;
		--blogzee-single-page-image-ratio-mobile: 0.55;
		--blogzee-widget-block-title-color : #333;
		--blogzee-footer-white-text: #fff;
		--blogzee-footer-title-text: #ffffff;
		--blogzee-footer-title-text-hover: #ffffff;
		--blogzee-single-content-color : #333;
		--border-bottom-color: #F4F4F4;
		--border-bottom-widget-color: #F4F4F4;
		--border-bottom-color-two: #f0f0f0;
		--border-bottom-color-three: #ffffff3b;
		--blogzee-carousel-bk-color: none;
		--blogzee-canvash-bk-color: #333333a3;

	/* Carousel */
		--blogzee-carousel-image-ratio: 0.65;
		--blogzee-carousel-image-ratio-tab: 0.65;
		--blogzee-carousel-image-ratio-mobile: 0.65;

		--blogzee-breadcrumb-link-color: #ffffff;
		--blogzee-breadcrumb-link-color-hover: #ffffff;
		--blogzee-breadcrumb-color : #ffffff;
		
	/* 404 Page */
		--blogzee-404-button-bkcolor: #FF376C;
		--blogzee-404-button-bkcolor-hover: #FF376C;

	/* footer */
		--blogzee-bottom-footer-link-color: #ffffff;
		--blogzee-bottom-footer-link-color-hover: #ffffff;		
		--blogzee-cateegory-collection-color: #000;

	/* Widget Button */
		--blogzee-widget-btn-color: #3858F6;
		--blogzee-widget-btn-color-hover: #3858F6;
		--blogzee-widget-btn-bk-color: #ffffff00;
		--blogzee-widget-btn-bk-color-hover: #ffffff00;
	}

	/** dark mode **/
		.blogzee-dark-mode {
			--blogzee-white-bk: #333333;
			--blogzee-post-title-font-color: #fff;
			--blogzee-date-font-color: #f1f1f1;
			--blogzee-post-content-font-color: #f8f8f8;
			--blogzee-meta-font-color: #f2f2f2;
			--blogzee-widget-block-title-color: #fff;
			--blogzee-menu-color-submenu: #fff;
			--blogzee-menu-color-submenu-hover: #f1f1f1;
			--blogzee-footer-menu-color: #fff;
			--blogzee-footer-menu-color-hover: #fff;
			--blogzee-single-content-color: #f8f8f8;
			--border-bottom-color: #5d5d5d;
			--border-bottom-color-two: #5d5d5d;
			--blogzee-canvas-icon-color: #ffffff;
			--blogzee-canvas-icon-color-hover: #ffffff;
			--blogzee-search-icon-color: #ffffff;
			--blogzee-search-icon-color-hover: #ffffff;
			--blogzee-banner-title-color: #ffffff;
			--blogzee-banner-meta-color: #fff;
			--blogzee-banner-excerpt-color: #fff;
			--blogzee-date-time-bk-color: #333333;
			--blogzee-top-header-bk-color: #000;
			--blogzee-date-time-color: #fff;
			--blogzee-header-social-color: #fff;
			--blogzee-footer-social-color: #fff;
			--blogzee-archive-text-color: #fff;
			--blogzee-breadcrumb-color: #fff;
			--blogzee-breadcrumb-link-color: #fff;
			--blogzee-custom-button-icon-color: #fff;
			--blogzee-custom-button-bk-color: #000;
			--blogzee-custom-button-icon-color: #fff;
			--blogzee-custom-button-icon-color-hover: #fff;
			--blogzee-widget-btn-color: #fff;
			--blogzee-widget-btn-bk-color: #ffffff00;
			--blogzee-custom-button-color: #fff;
			--blogzee-youmaymissed-block-title-color: #fff;
			--youmaymissed-section-bk-color: #333333;
			--blogzee-archive-pagination-color: #fff;
			--blogzee-archive-pagination-bk-color: #333333;
			--blogzee-time-color: #fff;
			--blogzee-date-color: #fff;
			--blogzee-footer-white-text: #fff;
			--blogzee-footer-title-text: #fff;
			--blogzee-youmaymissed-color: #fff;
			--blogzee-canvas-color: #fff;
			--blogzee-white-dark-color: #333333;
			--blogzee-black-dark-color: #fff;
			--blogzee-border-dark-color: #686868;
			--blogzee-banner-scrollbar-color: #838383;
			--blogzee-footer-widget-border-color: #ffffff2b;
		}

		body.blogzee-dark-mode:before {
			background: #222222;
		}

		body.blogzee-dark-mode .blogzee-breadcrumb-wrap {
			background: #333;
		}

		body.blogzee-dark-mode.single-post .comment-respond .comment-form-comment textarea,
		body.blogzee-dark-mode.single-post form.comment-form p input {
			background-color: #898989;
		}

		body.blogzee-dark-mode .widget_block.widget_search .wp-block-search__input {
			border: 2px solid #898989;
			background-color: #898989;
		}

		body.blogzee-dark-mode.error404 #blogzee-main-wrap #primary .not-found {
			background-color: #333333;
		}

		body.blogzee-dark-mode.archive.category #blogzee-main-wrap .page-header, 
		body.blogzee-dark-mode.archive.tag #blogzee-main-wrap .page-header, 
		body.blogzee-dark-mode.archive.date #blogzee-main-wrap .page-header, 
		body.blogzee-dark-mode.search.search-results #blogzee-main-wrap .blogzee-container .page-header,
		body.blogzee-dark-mode.archive.author .site #blogzee-main-wrap .page-header {
			background: rgb(51 51 51);
		}

		body.blogzee-dark-mode .widget_block.widget_search .wp-block-search__input {
			background-color: #bbbbbb;
		}

		body.blogzee-dark-mode .blogzee-carousel-section article.post-item .post-thumb:before,
		body.blogzee-dark-mode .blogzee-main-banner-section article.post-item .post-thumb:before {
			background-color: #a2a2a236;
		}

		body.single-post.blogzee-dark-mode #primary .blogzee-inner-content-wrap article .entry-content .wp-block-latest-posts a, 
		body.single-post.blogzee-dark-mode #primary .blogzee-inner-content-wrap article .entry-content .wp-block-categories a {
			color: #fff;
		}

		body.single-post.blogzee-dark-mode pre,
		body.single.blogzee-dark-mode #primary .blogzee-inner-content-wrap .wp-block-quote {
			background: #5b5b5b;
		}

		body.blogzee-dark-mode aside .widget,
		body.blogzee-dark-mode #widget_block,
		body.single-post.blogzee-dark-mode #blogzee-main-wrap .blogzee-container .row #primary .post-inner,
		body.single-post.blogzee-dark-mode #blogzee-main-wrap .blogzee-container .row #primary .comments-area,
		body.single-post.blogzee-dark-mode #primary article .post-card .bmm-author-thumb-wrap,
		body.single-post.blogzee-dark-mode #blogzee-main-wrap .blogzee-container .row #primary nav.navigation,
		body.single-post.blogzee-dark-mode #blogzee-main-wrap .blogzee-container .row #primary .single-related-posts-section-wrap {
			background: #333333;
		}

		body.blogzee-dark-mode .blogzee-main-banner-section.layout--four .post-elements,
		.blogzee-dark-mode footer.site-footer,
		body.blogzee-dark-mode .site-header {
			background: #333333;
		}

		body.blogzee-dark-mode .widget p,
		body.blogzee-dark-mode .widget h1,
		body.blogzee-dark-mode .widget h2,
		body.blogzee-dark-mode .widget h3,
		body.blogzee-dark-mode .widget h4,
		body.blogzee-dark-mode .widget h5,
		body.blogzee-dark-mode .widget h6{
			color: #ffffff;
		}

		@media (min-width: 48.1em) {
			body.blogzee-dark-mode .main-navigation ul.menu li:hover > ul, 
			body.blogzee-dark-mode .main-navigation ul.menu li.focus > ul, 
			body.blogzee-dark-mode .main-navigation ul.nav-menu li:hover > ul, 
			body.blogzee-dark-mode .main-navigation ul.nav-menu li.focus > ul {
				background: #333333;
			}

			body.blogzee-dark-mode .main-navigation .menu li.current-menu-item a, 
			body.blogzee-dark-mode .main-navigation .nav.menu li.current-menu-item a {
				color: #ffffff;
			}

			body.blogzee-dark-mode .main-navigation .menu li a, body.blogzee-dark-mode .main-navigation .nav.menu li a {
				color: #ffffff;
			}

		}

		body.blogzee-dark-mode .widget ul.wp-block-latest-posts li, 
		body.blogzee-dark-mode .widget ol.wp-block-latest-comments li, 
		body.blogzee-dark-mode .widget ul.wp-block-archives li, 
		body.blogzee-dark-mode .widget ul.wp-block-categories li, 
		body.blogzee-dark-mode .widget ul.wp-block-page-list li, 
		body.blogzee-dark-mode .widget .widget ul.menu li,
		body.blogzee-dark-mode aside .widget_blogzee_post_grid_widget .post-grid-wrap .post-item, 
		body.blogzee-dark-mode aside .widget_blogzee_post_list_widget .post-list-wrap .post-item,
		body.blogzee-dark-mode .canvas-menu-sidebar .widget_blogzee_post_list_widget .post-list-wrap .post-item, 
		body.blogzee-dark-mode .canvas-menu-sidebar ul.wp-block-latest-posts li, 
		body.blogzee-dark-mode .canvas-menu-sidebar ol.wp-block-latest-comments li, 
		body.blogzee-dark-mode .canvas-menu-sidebar  ul.wp-block-archives li, 
		body.blogzee-dark-mode .canvas-menu-sidebar  ul.wp-block-categories li, 
		body.blogzee-dark-mode .canvas-menu-sidebar ul.wp-block-page-list li, 
		body.blogzee-dark-mode .canvas-menu-sidebar .widget ul.menu li {
			border-color: #5a5a5a;
		}

		body.blogzee-dark-mode .widget ul.wp-block-latest-posts li:last-child, 
		body.blogzee-dark-mode .widget ol.wp-block-latest-comments li:last-child, 
		body.blogzee-dark-mode .widget ul.wp-block-archives li:last-child, 
		body.blogzee-dark-mode .widget ul.wp-block-categories li:last-child, 
		body.blogzee-dark-mode .widget ul.wp-block-page-list li:last-child, 
		body.blogzee-dark-mode .widget .widget ul.menu li:last-child,
		body.blogzee-dark-mode aside .widget_blogzee_post_grid_widget .post-grid-wrap .post-item:last-child, 
		body.blogzee-dark-mode aside .widget_blogzee_post_list_widget .post-list-wrap .post-item:last-child {
			border-bottom: none;
		}

		body.blogzee-dark-mode footer.site-footer .main-footer {
			background: #151515;
		}

		body.blogzee-dark-mode footer.site-footer .bottom-footer,
		body.blogzee-dark-mode header.site-header .main-header {
			background: #000;
		}
		body.blogzee-dark-mode .search-wrap.search-type--live-search .article-item {
			background: #333;
		}

		body.page.blogzee-dark-mode #blogzee-main-wrap #primary article.page,
		body.archive.author.blogzee-dark-mode .site #blogzee-main-wrap .page-header .blogzee-container .row {
			background: #333;
		}

		body.blogzee-dark-mode.boxed--layout #page {
			background: #000000;
		}

/*
=========================================
2.0 Normalize
=========================================
*/

html {
	 scroll-behavior: smooth;
}

body {
	font-family: var(--blogzee-main-font-family);
	scroll-behavior: smooth;
	font-size: var(--blogzee-main-font-size);
	letter-spacing: 0.3px;
}

.post,
.page {
	margin: 0;
}

input[type="text"],
input[type="email"] {
	width: 100%;
}

figure {
	margin: 0px;
}

.post-thumb img {
	display: block;
}

.blogzee-container-fluid {
	--bs-gutter-x: 1.5rem;
	--bs-gutter-x: 1.5rem;
	width: 100%;
	padding-right: var(--bs-gutter-x, 0.75rem);
	padding-left: var(--bs-gutter-x, 0.75rem);
	margin-right: auto;
	margin-left: auto;
	max-width: 120rem;
}

.blogzee-container-fluid .row {
	padding: 0 2rem;
}

.blogzee-container {
	--bs-gutter-x: 1.5rem;
	--bs-gutter-x: 1.5rem;
	width: 100%;
	padding-right: var(--bs-gutter-x, 0.75rem);
	padding-left: var(--bs-gutter-x, 0.75rem);
	margin-right: auto;
	margin-left: auto;
}

@media (max-width: 1080px) {
	.blogzee-container,
	#wp-custom-header {
		padding-left: 30px;
		padding-right: 30px;
	}
}

@media (max-width: 768px) {
	.blogzee-container,
	#wp-custom-header {
		padding-right: var(--bs-gutter-x, 0.75rem);
		padding-left: var(--bs-gutter-x, 0.75rem);
	}
}

.row {
	--bs-gutter-x: 1.5rem;
	--bs-gutter-y: 0;
	margin-top: calc(var(--bs-gutter-y) * -1);
	margin-right: calc(var(--bs-gutter-x) * -.5);
	margin-left: calc(var(--bs-gutter-x) * -.5);
}

#wp-custom-header {
	margin: 0 auto;
	padding-right: var(--bs-gutter-x, 0.6rem);
	padding-left: var(--bs-gutter-x, 0.6rem);
}

body.boxed--layout #wp-custom-header {
	margin-top: 0;
}

#wp-custom-header img {
	width: 100%;
	border-radius: 15px;
	overflow: hidden;
	display: block;
	margin: 20px 0;
}

.single figure.wp-block-image {
	margin: 0;
}

/*main container **/
	@media (max-with: 767px) {
		.blogzee-container,
		#wp-custom-header {
			max-width: 100%;
		}
	}

	@media (min-width: 768px) and (max-width: 991px) {
		.blogzee-container,
		#wp-custom-header {
			max-width: 100%;
		}
	}

	@media (min-width: 992px) {
	  .blogzee-container,
	  #wp-custom-header {
		max-width: 1080px;
	  }
	}

	@media (min-width: 1200px) {
	  .blogzee-container,
	  #wp-custom-header {
		max-width: 1140px;
	  }
	}

	@media (min-width: 1400px) {
	  .blogzee-container,
	  #wp-custom-header {
		max-width: 1250px;
	  }
	}

	@media (min-width: 1500px) {
	  .blogzee-container,
	  #wp-custom-header  {
		max-width: 1340px;
	  }
	}

body {
	background-color: #f3f3f3;
}

body:before {
	content: '';
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100vh;
	z-index: -1;
}

body .blogzee-inner-content-wrap article {
	margin: 0;
}

body .blogzee-inner-content-wrap article .blogzee-article-inne {
	box-shadow: var(--blogzee-article-box-shadow);
	-webkit-box-shadow: var(--blogzee-article-box-shadow);
}	

.boxed--layout #page {
	margin: 0 60px;
	background: #f9f9f9;
    box-shadow: 0px 0px 20px 0px rgb(0 0 0 / 3%);
}

/* Button Animation */
	.blogzee-button:after {
		position: absolute;
		content: "";
		width: 150%;
		height: 0%;
		left: 50%;
		top: 50%;
		transform: translateX(-50%) translateY(-50%) rotate(25deg);
		transition: all 0.75s;
		opacity: 0.5;
		z-index: -1;
	}
	
	.blogzee-button:hover:after {
		height: 400%;
		opacity: 1;
	}

/*
=========================================
3.0 Header
=========================================
*/

.site-header .site-title,
.site-footer .site-title {
	font-size: var(--blogzee-site-title-size);
	line-height: var(--blogzee-site-title-lineheight);
	letter-spacing: var(--blogzee-site-title-letterspacing);
	text-decoration: var(--blogzee-site-title-textdecoration);
	text-transform: var(--blogzee-site-title-texttransform);
	font-weight: var(--blogzee-site-title-weight);
	font-family: var(--blogzee-site-title-family);
	font-style: var(--blogzee-site-title-style);
}

.site-header .site-title,
.site-footer .site-title {
	color: var(--blogzee-black-dark-color);
}

.site-header .site-title a,
.site-footer .site-title a {
	text-decoration: none;
	color: inherit;
	letter-spacing: inherit;
	line-height: inherit;
	color: inherit;
}

.site-header .site-description,
.site-footer .site-description {
	font-size: var(--blogzee-site-description-size);
	line-height: var(--blogzee-site-description-lineheight);
	letter-spacing: var(--blogzee-site-description-letterspacing);
	text-decoration: var(--blogzee-site-description-textdecoration);
	text-transform: var(--blogzee-site-description-texttransform);
	font-weight: var(--blogzee-site-description-weight);
	font-family: var(--blogzee-site-description-family);
	color: var(--blogzee-black-dark-color);
	font-style: var(--blogzee-site-description-style);
	margin: 0;
	padding-top: 3px;
}

.main-header #site-navigation {
	display: inline-block;
	width: auto;
}

.main-header.header-sticky--enabled {
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 9999;
	background: var(--blogzee-site-bk-color);
	-webkit-transition: all 250ms ease;
	-o-transition: all 250ms ease;
	transition: all 250ms ease;
}

.main-header.header-sticky--enabled .site-branding-section {
	scale: 0.9;
	-webkit-transition: all 250ms ease;
	-o-transition: all 250ms ease;
	transition: all 250ms ease;
}

body.admin-bar .main-header.header-sticky--enabled .blogzee-container .row {
	padding: 35px 0 20px 0;
}

.main-header.header-sticky--enabled .blogzee-container .row {
	border-bottom: none;
	padding: 20px 0 20px 0;

	-webkit-transition: all 250ms ease;
	-o-transition: all 250ms ease;
	transition: all 250ms ease;
}

/** Menu **/
	@media (min-width: 48.1em){

		.main-navigation ul.menu li a.toggle-sub-menu,
		.main-navigation ul.nav-menu li a.toggle-sub-menu {
			display: none;
		}

		.main-navigation .menu ul,
		.main-navigation .nav-menu ul{
			list-style: none;
			line-height: 1.6;
			align-items: center;
			height: inherit;
			vertical-align: middle;
		}
		
		.main-navigation > div,
		.main-navigation ul.menu,
		.main-navigation ul.menu li,
		.main-navigation ul.nav-menu,
		.main-navigation ul.nav-menu li {
			height: inherit;
			vertical-align: middle;
			column-gap: 20px;
			row-gap: 10px;
		}

		.site-header .menu-background--enabled .nav-menu {
			background: var(--blogzee-menu-bk-color);
		}

		.site-header .menu-alignment--left.menu-background--enabled .nav-menu {
			padding-left: 15px;
		}

		.main-navigation .menu li,
		.main-navigation .nav-menu.menu li {
			display: flex;
			align-items: center;
		}

		.main-navigation .menu li a,
		.main-navigation .nav.menu li a {
			position: relative;
			font-size: var(--blogzee-menu-size);
			line-height: var(--blogzee-menu-lineheight);
			letter-spacing: var(--blogzee-menu-letterspacing);
		}

		body .main-navigation .menu li.current-menu-item > a,
		body .main-navigation .menu li.current_page_item > a {
			color: var(--blogzee-global-preset-theme-color);
		}

		.main-header.menu-alignment--left .main-navigation .menu li:first-child a,
		.main-header.menu-alignment--left .main-navigation .nav.menu li:first-child a{
			margin-left: 0;
		}

		.blogzee_dark_mode .main-navigation .menu li a,
		.blogzee_dark_mode .main-navigation .nav.menu li a {
			color: #fff;
		}

		.main-navigation .menu li a:hover,
		.main-navigation .nav.menu li a:hover{
			color: var(--blogzee-menu-color-hover);
		}
	
		.main-navigation ul.menu ul,
		.main-navigation ul.nav-menu ul {
			background-color: var(--blogzee-submenu-background-color);
			display: block;
			left: -999em;
			margin: 0;
			position: absolute;
			z-index: 110;
			padding: 5px 0;
			display: initial;
			box-shadow: 0px 9px 17px -10px rgb(0 0 0 / 58%);
			-webkit-box-shadow: 0px 9px 17px -10px rgb(0 0 0 / 58%);
			-moz-box-shadow: 0px 9px 17px -10px rgba(0,0,0,0.58);
			text-align: left;
		}

		.main-navigation ul.menu ul li,
		.main-navigation ul.nav-menu ul li {
			float: none;
			border-top: 0;
			position: relative;
		}

		.main-navigation ul.menu ul.sub-menu > li:last-child > a,
		.main-navigation ul.nav-menu ul.sub-menu > li:last-child > a {
			border-bottom: none;
		}

		.main-navigation ul.menu ul li a,
		.main-navigation ul.nav-menu ul li a  {
			font-size: 15px;
			min-width: 210px;
			width: 100%;
			color: var(--blogzee-menu-color-submenu);
			border-bottom: 1px solid var(--blogzee-submenu-border-btm-color);
			font-family: var(--blogzee-submenu-family);
			font-weight: var(--blogzee-submenu-weight);
			font-size: var(--blogzee-submenu-size);
			line-height: var(--blogzee-submenu-lineheight);
			letter-spacing: var(--blogzee-submenu-letterspacing);
			text-transform: var(--blogzee-submenu-texttransform);
			text-decoration: var(--blogzee-submenu-textdecoration);
			font-style: var(--blogzee-submenu-font-style);
		}

		.main-navigation ul.menu ul li a:after,
		.main-navigation ul.nav-menu ul li a:after  {
			color: var(--blogzee-menu-color-submenu);
		}

		.main-navigation ul.menu ul li a:hover,
		.main-navigation ul.nav-menu ul li a:hover  {
			color: var(--blogzee-menu-color-submenu-hover);
		}
		.main-navigation ul.menu ul li a:hover:after,
		.main-navigation ul.nav-menu ul li a:hover:after  {
			color: var(--blogzee-menu-color-submenu-hover);
		}

		.main-navigation ul.menu li:hover > ul, 
		.main-navigation ul.menu li.focus > ul,
		.main-navigation ul.nav-menu li:hover > ul, 
		.main-navigation ul.nav-menu li.focus > ul {
			right: auto;
			left: 0;
			height: auto;
			background: var(--blogzee-submenu-bk-color);
			border-radius: 2px;
		}

		.main-navigation ul.menu ul li:hover > ul, 
		.main-navigation ul.menu ul li.focus > ul,
		.main-navigation ul.nav-menu ul li:hover > ul, 
		.main-navigation ul.nav-menu ul li.focus > ul {
			right: auto;
			left: 100%;
		}

		/* arrow */
		.main-navigation ul.menu li.menu-item-has-children > a:after,
		.main-navigation ul.menu li.page_item_has_children > a:after,
		.main-navigation ul.nav-menu li.menu-item-has-children > a:after,
		.main-navigation ul.nav-menu li.page_item_has_children > a:after   {
			display: inline-block;
			font-family: "Font Awesome 5 Free";
			content: "\f107";
			font-style: normal;
			font-size: calc(var(--blogzee-menu-size)*0.77);
			font-weight: 900;
			margin-left: 8px;
		}

		.main-navigation ul.menu ul li.menu-item-has-children > a:after,
		.main-navigation ul.menu ul li.page_item_has_children > a:after,
		.main-navigation ul.nav-menu ul li.menu-item-has-children > a:after,
		.main-navigation ul.nav-menu ul li.page_item_has_children > a:after {
			display: inline-block;
			font-family: "Font Awesome 5 Free";
			content: "\f105";
			font-style: normal;
			font-size: 12px;
			font-weight: 900;
			line-height: 1;
			margin-left: 8px;
			float: right;
			line-height: inherit;
		}

		.main-navigation .menu > li li a,
		.main-navigation .nav-menu > li li a  {
			margin: 0;
			float: none;
			display: block;
			padding: 10px 25px;
			text-decoration: none;
		}

		.main-navigation .menu > li li a:hover,
		.main-navigation .nav-menu > li li a:hover  {
			transform: translateX(8px);
		}

		/* Custom More */
			.main-navigation .menu-item-custom-more > a:after {
				content: '\f141' !important;
			}
	}

	.main-navigation .menu li a,
	.main-navigation .nav.menu li a {
		transition: all .4s ease;
		transform-origin: 0 0;
		color: var(--blogzee-menu-color);
		font-family: var(--blogzee-menu-family);
		font-weight: var(--blogzee-menu-weight);
		text-transform: var(--blogzee-menu-texttransform);
		text-decoration: var(--blogzee-menu-textdecoration);
		font-style: var(--blogzee-menu-font-style);
	}

	/* search default */
	.site-header .search-wrap .search-form-wrap {
		display: none;
	}

	.site-header .search-wrap.search-type--default {
		position: relative;
	}

	.site-header .search-wrap.search-type--default.toggled .search-trigger {
		opacity: 0;
	}

	.site-header .search-wrap.search-type--default .search-form-wrap {
		position: absolute;
		width: 330px;
		height: auto;
		padding: 14px 15px;
		background-color: #fff;
		top: 43px;
		left: 0;
		z-index: 999999;
		box-shadow: 0px 1px 5px 3px rgb(0 0 0 / 3%);
		-webkit-box-shadow: 0px 1px 5px 3px rgb(0 0 0 / 3%);
		-moz-box-shadow: 0px 1px 5px 3px rgb(0 0 0 / 3%);
	}

	.site-header .column-1 .one.alignment-right .search-wrap.search-type--default .search-form-wrap,
	.site-header .column-1 .one.alignment-right .search-wrap.search-type--default .search-form-close,
	.site-header .column-2 .two .search-wrap.search-type--default .search-form-wrap,
	.site-header .column-2 .two .search-wrap.search-type--default .search-form-close,
	.site-header .column-3 .three .search-wrap.search-type--default .search-form-wrap,
	.site-header .column-3 .three .search-wrap.search-type--default .search-form-close,
	.site-header .column-4 .four .search-wrap.search-type--default .search-form-wrap,
	.site-header .column-4 .four .search-wrap.search-type--default .search-form-close {
		right: 0;
		left: initial;
	}

	.site-header .search-wrap.search-type--default .search-form-wrap .search-form {
		display: flex;
		text-align: center;
	}

	.site-header .search-wrap.search-type--default .search-form-wrap label {
		width: 100%;
		margin-right: 10px;
	}

	.site-header .search-wrap.search-type--default .search-form-wrap .search-field {
		width: 100%;
		font-size: 15px;
		padding: 7px 12px;
		border-radius: 0;
	}

	.site-header .search-wrap.search-type--default .search-form-wrap .search-submit {
		padding: 8px 15px 9px;
		line-height: 18px;
		margin: 0;
	}

	.search-form-wrap input.search-field:focus::placeholder,
	.search-form-wrap input.search-field {
		outline: none;
	}

	/* close btn */
	.search-form-close {
		color: var(--blogzee-search-icon-color);
		position: absolute;
		left: 0;
		cursor: pointer;
		line-height: 0;
		transition: all ease-in-out .3s;
		background-color: transparent;
		border: none;
		padding: 0;
		display: inline-block;
		font-size: 18px;
		top: -41px;
	}

	/* search popup */
	.site-header .search-wrap.search-type--live-search .search-form-close {
		z-index: 999;
		padding: 5px 11px;
		border-radius: 5px;
		text-align: center;
		margin: 0 auto;
		display: inline-block;
		color: var(--blogzee-black-dark-color);
		font-size: 24px;
		margin-top: 0;
		position: absolute;
		top: 50px;
		right: 50px;
		left: initial;
		border-color: transparent;
	}

	.site-header .search-wrap.search-type--live-search .search-form-close:hover {
		-webkit-transform: rotate(90deg);
		-ms-transform: rotate(90deg);
		transform: rotate(90deg);
	}

	.site-header .search-wrap .search-form-close:hover,
	.site-header .search-wrap:hover,
	.site-header .mode-toggle-wrap:hover,
	.site-header .mode-toggle:hover,
	.site-header .blogzee-canvas-menu:hover,
	.site-header .search-trigger:hover {
		cursor: pointer;
	}

	.site-header .mode-toggle i {
		font-size: 18px;
	}

	.site-header .mode-toggle img {
		width: 18px;
	}

	.blogzee-light-mode .site-header .mode-toggle i {
		color: var(--blogzee-theme-mode-color);
	}

	.blogzee-light-mode .site-header .mode-toggle:hover i {
		color: var(--blogzee-theme-mode-color-hover);
	}

	.blogzee-dark-mode .site-header .mode-toggle i {
		color: var(--blogzee-theme-darkmode-color);
	}

	.blogzee-dark-mode .site-header .mode-toggle:hover i {
		color: var(--blogzee-theme-darkmode-color-hover);
	}

	.blogzee-dark-mode .lightmode {
		display: none;
	}

	.blogzee-light-mode .darkmode {
		display: none;
	}

	.site-header .header-custom-button {
		text-decoration: none;
		padding: 5px 15px 5px 15px;
		background: var(--blogzee-custom-button-bk-color);
		border: none;
		text-align: center;
		display: flex;
		justify-content: right;
		-webkit-justify-content: right;
		align-items: center;
		-webkit-align-items: center;
		position: relative;
		overflow: hidden;
		z-index: 1;
	}

	.site-header .header-custom-button:after {
		background: var(--blogzee-custom-button-bk-color-hover);
	}

	.site-header .header-custom-button i {
		color: var(--blogzee-custom-button-icon-color);
		font-size: 12px;
	}

	.site-header .header-custom-button .custom-button-label {
		color: var(--blogzee-custom-button-color);
		font-family: var(--blogzee-custom-button-family);
		font-style: var(--blogzee-custom-button-style);
		font-size: var(--blogzee-custom-button-size);
		line-height: var(--blogzee-custom-button-lineheight);
		letter-spacing: var(--blogzee-custom-button-letterspacing);
		text-transform: var(--blogzee-custom-button-texttransform);
		text-decoration: var(--blogzee-custom-button-textdecoration);
		font-weight: var(--blogzee-custom-button-weight);
	}

	.site-header .header-custom-button:before {
		background: var(--blogzee-custom-button-bk-color-hover);
	}

	.site-header .header-custom-button:hover:before {
		top: -40%;
	}

	.site-header .header-custom-button:hover .custom-button-label {
		color: var(--blogzee-custom-button-color-hover); 
	}

	.site-header .header-custom-button:hover .custom-button-icon i {
		color: var(--blogzee-custom-button-icon-color-hover); 
	}

	.site-header .header-custom-button .custom-button-icon{
		padding-right: 9px;
	}

	body .site-header .header-custom-button .custom-button-icon img {
		vertical-align: middle;
	}

	.site-header .search-wrap {
		line-height: 17px;
	}

	.site-header .search-trigger {
		background: transparent;
		padding: 0;
		border: none;
	}

	.site-header .search-trigger i {
		color: var(--blogzee-search-icon-color);
	}

	.site-header .search-trigger:hover i {
		color: var(--blogzee-search-icon-color-hover);
	}

	/* Canvas Menu */
		.site-header .canvas-menu-icon {
			align-items: center;
			display: flex;
			height: 1.5rem;
			width: 1.8rem;
			justify-content: center;
			line-height: 0;
			margin: 0;
			overflow: hidden;
			padding: 0;
			position: relative;
			transform: scale(.8);
			transition: transform 0.6s cubic-bezier(0.46, 0.03, 0.52, 0.96);
			border: none;
			background: transparent;
			border-radius: 0;
			cursor: pointer;
		}

		.site-header .canvas-menu-icon span {
			background-color: var(--blogzee-canvas-icon-color);
			height: 2px;
			position: absolute;
			top: 50%;
			transition: transform 0.3s cubic-bezier(0.46, 0.03, 0.52, 0.96),background-color 0.3s cubic-bezier(0.46, 0.03, 0.52, 0.96);
			width: 2rem;
		}

		.site-header .canvas-menu-icon:hover span {
			background-color: var(--blogzee-canvas-icon-color-hover);
		}

		.site-header .canvas-menu-icon span:first-child {
			transform: scaleX(.5) translateY(-0.6rem);
			transform-origin: 100% 0;
		}

		.site-header .canvas-menu-icon span:nth-child(2) {
			transition: background-color 0.3s cubic-bezier(0.46, 0.03, 0.52, 0.96),transform 0.6s cubic-bezier(0.46, 0.03, 0.52, 0.96),opacity 0.6s cubic-bezier(0.46, 0.03, 0.52, 0.96);
		}

		.site-header .canvas-menu-icon span:nth-child(3) {
			transform: scaleX(.5) translateY(0.6rem);
			transform-origin: 0 50%;
		}

		.site-header .canvas-menu-icon:after {
			box-sizing: border-box;
			content: "";
			height: 100%;
			left: 0;
			position: absolute;
			top: 0;
			width: 100%;
		}

		/* Canvas icon hover */
			.site-header .canvas-menu-icon:hover span:first-child {
				transform: scaleX(1) translateY(-0.6rem);
			}

			.site-header .canvas-menu-icon:hover span:nth-child(3) {
				transform: scaleX(1) translateY(0.6rem);
			}

		/* Canvas icon animation */
			.blogzee-model-open .site-header .canvas-menu-icon span:first-child {
				animation: canvas-menu-1-active 1s forwards;
			}

			.blogzee-model-open .site-header .canvas-menu-icon span:nth-child(2) {
				opacity: 0;
				transform: translateX(100%);
			}

			.blogzee-model-open .site-header .canvas-menu-icon span:nth-child(3) {
				animation: canvas-menu-3-active 1s forwards;
			}

			@keyframes canvas-menu-1-active {
				0% {
					transform: translate3d(0,-0.6rem,0);
					transform-origin: left top;
				}
				
				50% {
					opacity: 0;
					transform: translate3d(-100%,-0.6rem,0);
					transform-origin: left top;
				}
				55% {
					opacity: 0;
					transform: rotate(45deg) translate3d(-40%,-0.78rem,0) scaleX(.7);
					transform-origin: left top;
				}
				100% {
					transform: rotate(45deg) translate3d(0,-0.78rem,0) scaleX(.7);
					transform-origin: left top;
				}
			}

			@keyframes canvas-menu-3-active {
				0% {
					transform: translate3d(0,0.6rem,0);
					transform-origin: left top;
				}
				
				50% {
					opacity: 0;
					transform: translate3d(-100%,0.6rem,0);
					transform-origin: left top;
				}
				55% {
					opacity: 0;
					transform: rotate(-45deg) translate3d(-40%,0.6rem,0) scaleX(.7);
					transform-origin: left top;
				}
				100% {
					opacity: 1;
					transform: rotate(-45deg) translate3d(0,0.6rem,0) scaleX(.7);
					transform-origin: left top;
				}
			}

	/** Light and dark Mode **/
	.site-header .mode-toggle-wrap {
		display: inline-block;
		margin-top: 5px;
	}

	.site-header .mode-toggle {
		background: transparent;
		border: none;
	}

	/* Mobile Menu  */

	#site-navigation button.menu-toggle {
		background: var(--blogzee-global-preset-theme-color);
		color: #fff;
		border: none;
		padding: 7px 15px;
	}

	#site-navigation #blogzee-menu-burger {
		display: inline-block;
		vertical-align: middle;
		margin-right: 6px;
	}

	#blogzee-menu-burger span {
		 width: 1.2em;
		height: 0.12em;
		margin: 0.28em 0;
		display: block;
		transition: all .4s ease;
		transform-origin: 0 0;
		background-color: #fff;
	}

	#blogzee-menu-burger .menu-txt {
		font-size: 22px;
		vertical-align: middle;
		line-height: 27px;
		padding-left: 5px;
	}

	.main-header.menu-alignment--left .site-navigation-wrapper {
		text-align: left;
	}

	.main-header.menu-alignment--center .site-navigation-wrapper {
		text-align: center;
	}

	.main-header.menu-alignment--right .site-navigation-wrapper {
		text-align: right;
	}

/* Header One */
	.site-header.layout--one .main-header .row > div,
	.site-header.layout--two .main-header .row .menu-section > div,
	.site-header.layout--three .main-header .row .menu-section > div {
		margin-right: 30px;
	}

	.site-header.layout--one .main-header .row > div:last-child,
	.site-header.layout--two .main-header .row .menu-section > div:last-child,
	.site-header.layout--three .main-header .row .menu-section > div:last-child {
		margin: 0;
	}

/* header two */
	.site-header.layout--two .main-header .blogzee-container .row {
		flex-direction: column;
	}

	.site-header.layout--two .blogzee-container .row .site-branding-section {
		flex: 1 1 100%;
		width: 100%;
		text-align: center;
		margin-bottom: 30px;
	}

	.site-header.layout--two .blogzee-container .row .menu-section {
		flex: 1 1 100%;
		width: 100%;
		display: flex;
		align-items: center;
	}

	.site-header.layout--two .menu-alignment--left .site-navigation-wrapper,
	.site-header.layout--three .menu-alignment--left .site-navigation-wrapper {
		text-align: left;
	}

	.site-header.layout--two .menu-alignment--center .row .site-navigation-wrapper,
	.site-header.layout--three .menu-alignment--center .row .site-navigation-wrapper {
		text-align: center;
	}

	.site-header.layout--two .menu-alignment--right .row .site-navigation-wrapper,
	.site-header.layout--three .menu-alignment--right .row .site-navigation-wrapper {
		text-align: right;
	}

/* header three */
	.site-header.layout--three .main-header .blogzee-container .row {
		flex-direction: column;
	}

	.site-header.layout--three .blogzee-container .site-branding-section {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30px;
		width: 100%;
	}

	.site-header.layout--three .blogzee-container .site-branding-section .site-branding {
		flex: 1 1 30%;
	}

	.site-header.layout--three .blogzee-container .site-branding-section .advertisement-banner {
		flex: 1 1 65%;
	}

	.advertisement-banner a,
	.advertisement-banner img {
		display: block;
	}

	.site-header.layout--one ~ .advertisement-banner,
	.site-header.layout--two ~ .advertisement-banner {
		margin-bottom: 35px;
		text-align: center;
	}

	.site-header.layout--three .menu-section {
		display: flex;
		align-items: center;
		width: 100%;
	}

	/* Menu Description */
	.main-navigation .menu.nav-menu .menu-item-description,
	.main-navigation .menu .menu-item-description {
		display: inline-block;
		margin-left: 7px;
		margin-top: -2px;
	}

	.main-navigation .menu.nav-menu .sub-menu .menu-item-description,
	.main-navigation .menu .sub-menu .menu-item-description {
    	position: absolute;
		width: 100%;
	}

	.main-navigation .menu.nav-menu .sub-menu .menu-item-description,
	.main-navigation .menu .sub-menu .menu-item-description {
		width: initial;
	}

	.bb-bldr--responsive .main-navigation .menu.nav-menu li .sub-menu .menu-item-description:after,
	.bb-bldr--responsive .main-navigation .menu li .sub-menu .menu-item-description:after {
		display: none;
	}

	.main-navigation .menu.nav-menu .menu-item-description .description-wrap,
	.main-navigation .menu .menu-item-description .description-wrap {
		background: #323232;
		color: #fff;
		padding: 0px 5px;
		font-size: 8px;
		letter-spacing: 0.6px;
		display: inline-block;
		border-radius: 6px;
		text-transform: uppercase;
		line-height: 16px;
	}

	.blogzee-dark-mode .main-navigation .menu.nav-menu .menu-item-description .description-wrap,
	.blogzee-dark-mode .main-navigation .menu .menu-item-description .description-wrap {
    	background: #555555;
	}

	.blogzee-dark-mode .main-navigation .menu.nav-menu li .menu-item-description:after,
	.blogzee-dark-mode .main-navigation .menu li .menu-item-description:after {
		border-top-color: #555555;
	}

/*
=========================================
4.0 Main Content (Archive)
=========================================
*/

.blogzee-inner-content-wrap article .content-wrap::-webkit-scrollbar,
.blogzee-table-of-content.display--fixed .toc-wrapper::-webkit-scrollbar {
	width: 2px;
}

.blogzee-inner-content-wrap article .content-wrap::-webkit-scrollbar-thumb,
.blogzee-table-of-content.display--fixed .toc-wrapper::-webkit-scrollbar-thumb {
	background-color: #ffffffd6;
	border-radius: 40px;
}

.blogzee-inner-content-wrap article .content-wrap::-webkit-scrollbar-track,
.blogzee-table-of-content.display--fixed .toc-wrapper::-webkit-scrollbar-track {
	box-shadow: inset 0 0 5px #00000047;
	border-radius: 40px;
}

body:not(.archive--grid-two-layout) .site-main article .blogzee-article-inner {
	background: var(--blogzee-white-dark-color);
}

.entry-title a {
	color: inherit;
}

.blogzee-inner-content-wrap.archive-align--left article .inner-content {
	text-align: left;
}

.blogzee-inner-content-wrap.archive-align--left article.format-image .inner-content,
.blogzee-inner-content-wrap.archive-align--left article.format-quote .inner-content {
	text-align: left!important;
}

.blogzee-inner-content-wrap.archive-align--right article .inner-content {
	text-align: right;
}

.blogzee-inner-content-wrap.archive-align--right article.format-image .inner-content,
.blogzee-inner-content-wrap.archive-align--right article.format-quote .inner-content {
	text-align: right!important;
}

.blogzee-inner-content-wrap.archive-align--right article.format-image .inner-content .post-date.posted-on {
	padding-right: 15px;
}

/* Sticky */
.site-main article.sticky .blogzee-article-inner {
	border: 2px solid var(--blogzee-global-preset-theme-color);
}

.archive--grid-two-layout .site-main article.sticky .post-thumbnail-wrapper:after,
.site-main article.sticky .blogzee-article-inner .inner-content:after {
	content: '\f08d';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    background: var(--blogzee-global-preset-theme-color);
    position: absolute;
    bottom: 15px;
    color: #fff;
    left: 15px;	
    border-radius: 50%;
    font-size: 13px;
    width: 34px;
    height: 34px;
    text-align: center;
    line-height: 34px;
    transform: rotate(45deg);
	z-index: 2;
}

.archive--grid-two-layout .site-main article.sticky .post-thumbnail-wrapper:after {
	display: none;
}

.archive--grid-two-layout .site-main article.sticky .blogzee-article-inner .inner-content:after {
	top: -10px;
    right: -10px;
    left: initial;
}

	body:not(.archive--grid-two-layout) #blogzee-main-wrap > .blogzee-container > .row #primary article .blogzee-article-inner {
		padding: 20px;
	}

/*
=========================================
4.1 (Grid)
=========================================
*/

	body #blogzee-main-wrap > .blogzee-container > .row {
		padding-bottom: 40px;
	}

	/* image post format */
	body.archive--grid-two-layout .blogzee-inner-content-wrap article.post-format figure.post-thumbnail-wrapper:before {
		padding-bottom: calc( 100% * var(--blogzee-archive-post-image-ratio) * 1.5 );
	}

	@media (max-width: 940px) {
		body.archive--grid-two-layout .blogzee-inner-content-wrap article.post-format figure.post-thumbnail-wrapper:before {
			padding-bottom: calc( 100% * var(--blogzee-archive-post-image-ratio) * 1.7 );
		}
	}

	@media (max-width: 610px) {
		body.archive--grid-two-layout .blogzee-inner-content-wrap article.post-format figure.post-thumbnail-wrapper:before {
			padding-bottom: calc( 100% * var(--blogzee-archive-post-image-ratio) * 1.5 );
		}
	}

	/* image post format list layout */
	body.archive--list-two-layout .blogzee-inner-content-wrap article.post-format figure.post-thumbnail-wrapper:before {
		padding-bottom: calc( 100% * var(--blogzee-archive-post-image-ratio) * 0.62 );
	}

	@media (max-width: 940px) {
		body.archive--list-two-layout .blogzee-inner-content-wrap article.post-format figure.post-thumbnail-wrapper:before {
			padding-bottom: calc( 100% * var(--blogzee-archive-post-image-ratio)  * 0.62 );
		}
	}

	@media (max-width: 610px) {
		body.archive--list-two-layout .blogzee-inner-content-wrap article.post-format figure.post-thumbnail-wrapper:before {
			padding-bottom: calc( 100% * var(--blogzee-archive-post-image-ratio) );
		}
	}

	.post-categories .cat-item a {
		background-color: var(--blogzee-category-bk-color);
		display: inline-block;
		color: var(--blogzee-category-font-color);
		text-decoration: none;
		font-family: var(--blogzee-category-font-family);
		font-style: var(--blogzee-category-font-style);
		font-weight: var(--blogzee-category-font-weight);
		font-size: calc( var(--blogzee-category-font-size) * 0.76 );
		line-height: var(--blogzee-category-font-lineheight);
		letter-spacing: var(--blogzee-category-font-letterspacing);
		text-transform: var(--blogzee-category-font-texttransform);
		text-decoration: var(--blogzee-category-font-textdecoration);
		padding: 2px 12px;
		transition: all 0.25s ease-in 0s;
		border-radius: 50px;
	}

	.post-date {
		color: var(--blogzee-date-font-color);
		text-decoration: none;
		font-family: var(--blogzee-date-font-family);
		text-transform: var(--blogzee-date-font-texttransform);
		text-decoration: var(--blogzee-date-font-textdecoration);
		font-weight: var(--blogzee-date-font-weight);
		margin: 0 0 10px 0;
		display: inline-block;
		font-size: var(--blogzee-date-font-size);
		line-height: var(--blogzee-date-font-lineheight);
		font-style: var(--blogzee-date-font-style);
	}

	article.format-image .post-date {
		color: var(--blogzee-white-text);
	}

	.post-date a {
		text-decoration: none;
		color: inherit;
		font-size: inherit;
		font-weight: inherit;
		letter-spacing: var(--blogzee-date-font-letterspacing);
	}

	.post-date.published a .updated,
	.post-date.modified a .published {
		display: none;
	}

	.post-date.published a .published,
	.post-date.modified a .updated {
		display: inline-block;
	}

	.post-date i {
		padding-right: 8px;
	}

	article .entry-title {
		font-family: var(--blogzee-post-content-font-family);
		font-style: var(--blogzee-post-content-font-style);
		font-size: var(--blogzee-post-content-font-size);
		color: var(--blogzee-post-title-font-color);
		font-weight: var(--blogzee-post-content-font-weight);
		line-height: var(--blogzee-post-content-font-lineheight);
		letter-spacing: var(--blogzee-post-content-font-letterspacing);
		text-transform: var(--blogzee-post-content-font-texttransform);
		text-decoration: var(--blogzee-post-content-font-textdecoration);
		padding: 0;
		margin: 5px 0 10px 0;
		position: relative;
		word-break: break-word;
		/* 限制显示为2行 */
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	article h2.entry-title a {
		color: inherit;
		text-decoration: none;
		letter-spacing: inherit;
	}

	article .post-excerpt {
		/* 隐藏post description */
		display: none;
	}

	/* 隐藏continue reading按钮 */
	article .content-wrap .post-button {
		display: none;
	}

	article .post-excerpt p {
		margin: 0;
	}

	article .post-meta .byline {
		text-align: left;
		font-family: var(--blogzee-author-font-family);
		font-weight: var(--blogzee-author-font-weight);
		font-size: var(--blogzee-author-font-size);
		letter-spacing: var(--blogzee-author-font-letterspacing);
		line-height: var(--blogzee-author-font-lineheight);
		text-transform: var(--blogzee-author-font-texttransform);
		text-decoration: var(--blogzee-author-font-textdecoration);
		font-style: var(--blogzee-author-font-style);
		color: var(--blogzee-meta-font-color);
		margin-inline: 7px;
	}

	article .blogzee-article-inner .post-meta .byline {
		margin-left: 0;
	}

	article .post-meta .byline a {
		text-decoration: inherit;
		color: inherit;
		font-size: inherit;
		text-transform: inherit;
	}

	article .post-meta .byline img {
		vertical-align: middle;
		border-radius: 50%;
		margin-right: 8px;
		margin-top: -2px;
		width: 20px;
		height: 20px;
	}

	article .post-meta {
		font-family: var(--blogzee-meta-font-family);
		font-size: var(--blogzee-meta-font-size);
		font-weight: var(--blogzee-meta-font-weight);
		color: var(--blogzee-meta-font-color);
		margin-bottom: 10px;
	}

	article .post-meta .post-read-time {
		font-family: var(--blogzee-readtime-font-family);
		font-weight: var(--blogzee-readtime-font-weight);
		text-decoration: var(--blogzee-readtime-font-textdecoration);
		text-transform: var(--blogzee-readtime-font-texttransform);
		font-style: var(--blogzee-readtime-font-style);
		font-size: var(--blogzee-readtime-font-size);
		letter-spacing: var(--blogzee-meta-font-letterspacing);
		line-height: var(--blogzee-meta-font-lineheight);
		display: inline-block;
		margin: 5px 7px;
	}

	article .post-meta .post-read-time i {
		padding-left: 0;
    	padding-right: 8px;
	}

	article .post-meta a {
		text-decoration: none;
		color: inherit;
	}

	article .post-meta i {
		padding-left: 8px;
	}

	article .post-meta .post-comments-num {
		margin: 5px 7px;
		display: inline-block;
		font-family: var(--blogzee-comment-font-family);
		font-weight: var(--blogzee-comment-font-weight);
		font-style: var(--blogzee-comment-font-style);
		font-size: var(--blogzee-comment-font-size);
		letter-spacing: var(--blogzee-comment-font-letterspacing);
		line-height: var(--blogzee-comment-font-lineheight);
		text-transform: var(--blogzee-comment-font-texttransform);
		text-decoration: var(--blogzee-comment-font-textdecoration);
	}

	article .content-wrap .post-button {
		display: inline-block;
		color: var(--blogzee-black-dark-color);
		background: transparent;
		line-height: 1;
		text-decoration: none;
		margin-top: 20px;
		position: relative;
		overflow: hidden;
		z-index: 1;
		transition: all .3s ease-in-out;
	}

	article .content-wrap .post-button:hover {
		color: var(--blogzee-global-preset-theme-color);
	}

	article .content-wrap .post-button .button-text {
		font-family: var(--blogzee-readmore-font-family);
		font-weight: var(--blogzee-readmore-font-weight);
		font-style: var(--blogzee-readtime-font-style);
		font-size: var(--blogzee-readmore-font-size);
		letter-spacing: var(--blogzee-readmore-font-letterspacing);
		line-height: var(--blogzee-readmore-font-lineheight);
		text-transform: var(--blogzee-readmore-font-texttransform);
		text-decoration: var(--blogzee-readmore-font-textdecoration);
	}

	article.format-quote .content-wrap .post-button,
	article.format-image .content-wrap .post-button {
		color: #fff;
		background: transparent;
		padding: 0;
		border: none;
	}

	.blogzee-dark-mode article .content-wrap .post-button {
		color: #fff;
		border-color: #fff;
		background-color: transparent;		
		box-shadow: none;
		padding: 0;
		border: none;
	}

	article .content-wrap .post-button .button-icon {
		margin-left: 10px;
	}

	article .content-wrap .post-button a {
		color: inherit;
	}

	article.post_format-post-format-gallery .post-thumbnail-wrapper {
		display: grid;
	}

	/* grid layout two */
	article .content-wrap .button--wrapper {
		margin-top: 18px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 1px dashed #e7e7e7;
		padding-top: 15px;
		border-image: repeating-linear-gradient(to right, #DBDBDA 0 10px, transparent 0px 16px);
		border-image-slice: 1;
	}

	.archive--grid-two-layout.blogzee-dark-mode article .content-wrap .button--wrapper {
		border-image: repeating-linear-gradient(to right, #6c6c6c 0 10px, transparent 0px 16px);
		border-image-slice: 1;
	}

	.archive--grid-two-layout .align-off article .content-wrap .button--wrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.archive--grid-two-layout .archive-align--center article .content-wrap .button--wrapper .post-date {
		text-align: right;
	}

	.archive--grid-two-layout .archive-align--center article .content-wrap .button--wrapper .post-date {
		text-align: right;
	}

	article .content-wrap .button--wrapper .post-button,
	article .content-wrap .button--wrapper .post-date {
		margin: 0;
	}

/*
=========================================
4.2 List
=========================================
*/

	body.archive--list-two-layout #blogzee-main-wrap > .blogzee-container > .row #primary .blogzee-inner-content-wrap {
		display: grid;
		gap: 30px;
	}

	body #primary article .blogzee-article-inner .entry-title {
		margin-top: 0;
	}

	body.archive--list-two-layout:not(.archive-image--stretched) .blog-inner-wrapper {
		align-items: flex-start;
	}

	body.blogzee-dark-mode article .blogzee-article-inner {
		border-color: #575757;
	}

	body .blogzee-inner-content-wrap article figure.post-thumbnail-wrapper:before {
		content: '';
		display: block;
		padding-bottom: calc( 100% * var(--blogzee-archive-post-image-ratio) );
	}

	.home .blogzee-inner-content-wrap .post_format-post-format-video .wp-block-video,
	body .blogzee-inner-content-wrap article .post-thumnail-inner-wrapper {
		height: 100%;
		width: 100%;
		position: absolute;
		top: 0;
		left: 0;
		background-color: #e7e7e7e7;
		overflow: hidden;
	}

	body .blogzee-inner-content-wrap .post_format-post-format-video .blogzee-article-inner .post-thumnail-inner-wrapper iframe,
	body .blogzee-inner-content-wrap .post_format-post-format-audio .blogzee-article-inner .post-thumnail-inner-wrapper iframe {
		width: 100%;
		height: 100%;
	}

	body .blogzee-inner-content-wrap .post_format-post-format-video .blogzee-article-inner .post-thumnail-inner-wrapper video {
		width: 100%;
		height: 100%;
		object-fit: cover;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: scale(1) translate(-50%, -50%);
	}

	body .blogzee-inner-content-wrap article figure.post-thumbnail-wrapper .post-thumnail-inner-wrapper img {
		display: block;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	article.post_format-post-format-image .post-thumbnail-wrapper .blogzee-inner-content-wrap-fi {
		padding-left: 15px;
		padding-right: 15px;
	}

	body #blogzee-main-wrap > .blogzee-container > .row #primary article {
		position: relative;
	}

	/*  Social Share  */
		article figure.post-thumbnail-wrapper {
			overflow: hidden;
		}

		.post-thumbnail-wrapper .post-format-ss-wrap {
			position: absolute;
			right: 15px;
			top: 10px;
			z-index: 999;
		}

		.post-format-ss-wrap .post-format-icon {
			display: block;
			width: 35px;
			height: 35px;
			line-height: 37px;
			text-align: center;
			font-family: sans-serif;
			font-size: 16px;
			color: #3f434c;
			background-color: #fffc;
			border-radius: 50%;
			opacity: 0;
			animation-name: pupout;
			-webkit-animation-name: pupout;
			animation-duration: 0.4s;
			-webkit-animation-duration: 0.4s;
			animation-timing-function: ease;
			-webkit-animation-timing-function: ease;
			visibility: visible;
		}

		.post-thumbnail-wrapper:hover .post-format-ss-wrap .post-format-icon {
			opacity: 1;
			animation-name: pupin;
			-webkit-animation-name: pupin;
			animation-duration: 0.4s;
			-webkit-animation-duration: 0.4s;
			animation-timing-function: ease;
			-webkit-animation-timing-function: ease;
			visibility: visible;
		}

		@keyframes pupin {
			0% {
				transform: scale(0);
				opacity: 0;
			}
			
			50% {
				transform: scale(1.15);
			}
			100% {
				transform: scale(1);
				opacity: 1;
			}
		}

		@keyframes pupout {
			0% {
				transform: scale(1);
				opacity: 1;
			}
			
			100% {
				transform: scale(1.3);
				opacity: 0;
			}
		}

		/* Post Format Icon */
			.post-format-icon {
				display: inline-block;
				align-self: self-start;
				color: #fff;
				border-radius: 3px;
				width: 34px;
				height: 31px;
				text-align: center;
				line-height: 32px;
				font-size: 13px;
				overflow: hidden;
				background: #00000080;
			}

			.post-format-icon.type--svg {
				background: transparent;
			}

	/** author archive **/
	body.archive.author #blogzee-main-wrap .page-header .blogzee-container img {
		border-radius: 50%;
		overflow: hidden;
		border: 3px solid #eaeaea;
		width: 100px;
		height: 100px;
	}

	body.archive.author #blogzee-main-wrap .page-header .blogzee-container .page-description{
		font-size: 24px;
		color: var(--blogzee-white-text);
		letter-spacing: 0.6px;
		line-height: 30px;
		text-decoration: none;
		text-transform: capitalize;
	}

/*
=========================================
4.6 List Two
=========================================
*/

	body.archive--list-two-layout #primary .archive-align--center article .blogzee-article-inner {
		text-align: center;
	}

	body.archive--list-two-layout #primary .archive-align--right article .blogzee-article-inner {
		text-align: right;
	}

	body.archive--list-two-layout #primary .blogzee-article-inner {
		position: relative;
		overflow: hidden;
	}

	body.archive--list-two-layout #primary .blogzee-article-inner .blog-inner-wrapper .inner-content {
		padding-top: 0;
	}

	body.archive--list-two-layout #primary .blogzee-article-inner .entry-title {
		margin-bottom: 25px;
		padding-bottom: 12px;
		border-bottom: 1px solid #e3e3e3;
	}

	body.archive--list-two-layout.blogzee-dark-mode #primary .blogzee-article-inner .entry-title {
		border-color: #6c6c6c;
	}

	body.archive--list-two-layout #primary .blog-inner-wrapper {
		display: flex;
		gap: 18px;
	}

	body.archive--list-two-layout #primary .blog-inner-wrapper .post-thumbnail-wrapper {
		order: 2;
		flex: 0 1 40%;
		position: relative;
	}

	body.archive--list-two-layout #primary .blog-inner-wrapper .inner-content {
		flex: 1 1 60%;
	}

	body.archive--list-two-layout #primary .post-meta .post-date {
		display: inline-block;
		margin-right: 5px;
		margin-bottom: 0;
	}

	body.archive--list-two-layout #primary .post-thumbnail-wrapper .post-categories {
		padding: 0;
		margin: 0;
		list-style: none;
		position: absolute;
		z-index: 2;
		top: 15px;
		left: 15px;
	}

	/* Post Format Image */
		body.archive--list-two-layout #primary article.post-format .post-thumbnail-wrapper {
			position: relative;
		}

		body.archive--list-two-layout article.format-image .inner-content .content-wrap .blogzee-inner-content-wrap-fi::-webkit-scrollbar,
		body.archive--list-two-layout article.format-quote .inner-content .content-wrap .blogzee-inner-content-wrap-fi::-webkit-scrollbar {
			width: 2px;
		}

		body.archive--list-two-layout article.format-image .inner-content .content-wrap .blogzee-inner-content-wrap-fi::-webkit-scrollbar-thumb,
		body.archive--list-two-layout article.format-quote .inner-content .content-wrap .blogzee-inner-content-wrap-fi::-webkit-scrollbar-thumb {
			background-color: var(--blogzee-global-preset-theme-color);
			border-radius: 40px;
		}

		body.archive--list-two-layout #primary article.format-image .blogzee-article-inner .entry-title {
			padding: 0;
			border: none;
			margin: 5px 0 10px 0;
		}

/*
=========================================
4.8 Grid Two
=========================================
*/

	.archive--grid-two-layout #primary .blogzee-inner-content-wrap {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 30px;
	}

	.archive--grid-two-layout #primary .archive-align--center article .inner-content {
		text-align: center;
	}

	/* Figure */
	.archive--grid-two-layout #primary .post-thumbnail-wrapper {
		position: relative;
	}

	.archive--grid-two-layout #primary .post-thumbnail-wrapper .post-categories {
		position: absolute;
		top: 15px;
		left: 15px;
		padding: 0;
		margin: 0;
		list-style: none;
		z-index: 2;
	}

	.archive--grid-two-layout #primary article:not(.post-format) .inner-content {
		width: 93%;
		background: var(--blogzee-white-dark-color);
		margin-top: -70px;
		position: relative;
		padding: 18px 22px;
		z-index: 2;
	}

	body.archive--grid-two-layout #primary article:not(.post-format) .inner-content {
		border-top-left-radius: 0;
		overflow: hidden;
	}


/*
=========================================
5.0 Sidebar
=========================================
*/
/* right sidebar */
body.archive--right-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.single--right-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.search-page--right-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.page--right-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.error-page--right-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.search-page--right-sidebar #blogzee-main-wrap .blogzee-container > .row {
	display: flex;
}

body.archive--right-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.single--right-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.search-page--right-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.page--right-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.error-page--right-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.search-page--right-sidebar #blogzee-main-wrap .blogzee-container .row #primary{
	margin-right: 15px;
	flex: 1 1 70%;
	max-width: 70%;
}

body.archive--right-sidebar #blogzee-main-wrap .blogzee-container .row #secondary,
body.single--right-sidebar #blogzee-main-wrap .blogzee-container .row #secondary,
body.search-page--right-sidebar #blogzee-main-wrap .blogzee-container .row #secondary,
body.page--right-sidebar #blogzee-main-wrap .blogzee-container .row #secondary,
body.error-page--right-sidebar #blogzee-main-wrap .blogzee-container .row #secondary,
body.search-page--right-sidebar #blogzee-main-wrap .blogzee-container .row #secondary {
	margin-left: 15px;
	flex: 0 2 30%;
	max-width: 30%;
}

/* left sidebar */
body.archive--left-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.single--left-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.search-page--left-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.page--left-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.error-page--left-sidebar #blogzee-main-wrap .blogzee-container > .row {
	display: flex;
	justify-content: flex-end;
}

body.archive--left-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.single--left-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.search-page--left-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.page--left-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.error-page--left-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.search-page--left-sidebar #blogzee-main-wrap .blogzee-container .row #primary {
	margin-left: 15px;	
	flex: 1 1 70%;
	max-width: 70%;
}

body.archive--left-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside,
body.single--left-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside,
body.search-page--left-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside,
body.page--left-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside,
body.error-page--left-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside {
	margin-right: 15px;
	flex: 0 2 30%;
	max-width: 30%;
}

/* both sidebar */
body.archive--both-sidebar #blogzee-main-wrap .blogzee-container > .row, 
body.single--both-sidebar #blogzee-main-wrap .blogzee-container > .row, 
body.search-page--both-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.page--both-sidebar #blogzee-main-wrap .blogzee-container > .row,
body.error-page--both-sidebar #blogzee-main-wrap .blogzee-container > .row {
	display: flex;
	justify-content: center;
}

body.archive--both-sidebar #blogzee-main-wrap > .blogzee-container .row #primary, 
body.single--both-sidebar #blogzee-main-wrap > .blogzee-container .row #primary, 
body.search-page--both-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.page--both-sidebar #blogzee-main-wrap > .blogzee-container .row #primary,
body.error-page--both-sidebar #blogzee-main-wrap > .blogzee-container .row #primary   {
	margin-left: 15px;	
	margin-right: 15px;
}

body.archive--both-sidebar #blogzee-main-wrap .blogzee-container .row main.site-main,
body.single--both-sidebar #blogzee-main-wrap .blogzee-container .row main.site-main, 
body.search-page--both-sidebar #blogzee-main-wrap .blogzee-container .row main.site-main,
body.page--both-sidebar #blogzee-main-wrap .blogzee-container .row main.site-main,
body.error-page--both-sidebar #blogzee-main-wrap .blogzee-container .row main.site-main {
	flex: 1 1 50%;
	max-width: 50%;
}

body.archive--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary,
body.single--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary,
body.search-page--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary,
body.page--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary,
body.error-page--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary{
	margin-left: 15px;
	flex: 0 1 25%;
	max-width: 25%;
}

body.archive--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside,
body.single--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside,
body.search-page--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside,
body.page--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside,
body.error-page--both-sidebar #blogzee-main-wrap .blogzee-container .row #secondary-aside {
	margin-right: 15px;
	flex: 0 1 25%;
	max-width: 25%;
}

/*
=========================================
6.0 Widgets
=========================================
*/
section.widget.widget_block.widget_media_image {
	background: transparent;
	padding: 0;
	box-shadow: none;
	overflow: hidden;
}
	
body .widget + .widget {
	margin: 30px 0 0 0;
}

body footer .widget + .widget {
	margin: 25px 0 0;
}

footer .widget,
body .widget.widget_block .wp-block-heading {
	margin: 0;
}

body .widget.widget_block .wp-block-group__inner-container .wp-block-heading {
	margin-bottom: 10px;
}

aside .widget,
#widget_block {
	background: var(--blogzee-white-bk);
	padding: 15px 25px 25px;
	margin: 0;
}

.widget_block .wp-block-group__inner-container .wp-block-heading,
section.widget .widget-title,
.widget_block.widget_search .wp-block-search__label,
footer .wp-block-heading {
	font-family: var(--blogzee-widget-block-font-family);
	font-weight: var(--blogzee-widget-block-font-weight);
	font-style: var(--blogzee-widget-block-font-style);
	font-size: calc( var(--blogzee-widget-block-font-size) * 0.94 );
	letter-spacing: var(--blogzee-widget-block-font-letterspacing);
	line-height: var(--blogzee-widget-block-font-lineheight);
	margin-bottom: 15px;
	margin-top: 0;
	padding-bottom: 5px;
	display: block;
	color: var(--blogzee-widget-block-title-color);
	position: relative;
	text-transform: var(--blogzee-widget-block-font-texttransform);
	text-decoration: var(--blogzee-widget-block-font-textdecoration);
	overflow: hidden;
}

.widget_block .wp-block-group__inner-container .wp-block-heading,
section.widget .widget-title span {
	position: relative;
}

.block-title--one .widget_block .wp-block-group__inner-container .wp-block-heading:after,
.block-title--one section.widget .widget-title span:after,
.block-title--one .wp-block-search__label:after,
.block-title--one .blogzee-you-may-have-missed-section .section-title:after,
.block-title--one .single-related-posts-section .blogzee-block-title:after {
	content: '';
	position: absolute;
	width: 4px;
	height: 16px;
	background: var(--blogzee-global-preset-theme-color);
	top: 3px;
	left: 0;
	line-height: 20px;
}

.block-title--one .widget_block .wp-block-group__inner-container .wp-block-heading:after,
.block-title--one .widget .wp-block-search__button-outside .wp-block-search__label:after {
	top: 9px;
}

.block-title--one .widget_block .wp-block-group__inner-container .wp-block-heading,
.block-title--one section.widget .widget-title span,
.block-title--one .widget .wp-block-search__button-outside .wp-block-search__label,
.block-title--one .blogzee-you-may-have-missed-section .section-title {
	padding-left: 15px;
}

.widget .wp-block-search__inside-wrapper {
	position: relative;
}

.widget_block.widget_search .wp-block-search__input {
	width: 100%;
	height: 42px;
	border-radius: 50px;
	border: none;
	border: 1px dashed #dbdbdb;
	padding: 0 15px;
	background-color: #f8f8f875;
}

.widget_block.widget_search .wp-block-search__input:focus {
	box-shadow: none;
	outline-offset: 0;
	outline-color: var(--blogzee-global-preset-theme-color);
	min-width: 1px;
}

.widget .wp-block-search__button,
.widget.widget_search .search-form  .search-submit {
	position: absolute;
	top: 5px;
	right: 6px;
	color: var(--blogzee-white-text);
	background-color: var(--blogzee-global-preset-theme-color);
	border: none;
	font-weight: 500;
	padding: 8px 10px 9px 10px;
	border-radius: 50px;
	font-size: 0;
}

.widget.widget_search .search-form  .search-submit {
	font-size: 13px;
	border-radius: 15px;
}

button.wp-block-search__button.wp-element-button:before,
.widget.widget_search .search-form .search-submit:before {
	font-family: 'Font Awesome 5 Free';
	content: '\f002';
	color: #fff;
	font-weight: 900;
	display: inline-block;
	font-size: 13px;
	line-height: 15px;
	padding-top: 1px;
}

body .widget ul.wp-block-latest-posts li:last-child, 
body .widget ol.wp-block-latest-comments li:last-child, 
body .widget ul.wp-block-archives li:last-child, 
body .widget ul.wp-block-categories li:last-child, 
body .widget ul.wp-block-page-list li:last-child, 
body .widget .widget ul.menu li:last-child, 
body aside .widget_blogzee_post_grid_widget .post-grid-wrap .post-item:last-child, 
body aside .widget_blogzee_post_list_widget .post-list-wrap .post-item:last-child {
	border-bottom: none;
}

.mode-toggle-wrap .mode-toggle {
	color: #fff;
}

.widget.widget_block.widget_text p {
	margin: 0;
}

footer .wp-block-group__inner-container p {
	margin: 0;
}

footer .wp-block-group__inner-container p + p {
    margin-top: 20px;
}

/* block title layout two */
	body.block-title--two .widget_block .wp-block-group__inner-container .wp-block-heading,
	body.block-title--two section.widget .widget-title span,
	body.block-title--two .widget .widget_block.widget_search .wp-block-search__label,
	.block-title--two .single-related-posts-section .blogzee-block-title,
	.block-title--two .blogzee-you-may-have-missed-section .section-title {
		display: flex;
		gap: 15px;
		align-items: center;
	}

	.block-title--two .widget_block .wp-block-group__inner-container .wp-block-heading:after,
	.block-title--two section.widget .widget-title span:after,
	.block-title--two .widget .wp-block-search__button-outside .wp-block-search__label:after,
	.block-title--two .blogzee-you-may-have-missed-section .section-title:after,
	.block-title--two .single-related-posts-section .blogzee-block-title:after {
		content: '';
		position: relative;
		flex: 1;
		height: 6px;
		background: rgba(128, 128, 128, .1);
		border-radius: 50px;
	}

	.block-title--two.blogzee-dark-mode .widget_block .wp-block-group__inner-container .wp-block-heading:after,
	.block-title--two.blogzee-dark-mode section.widget .widget-title span:after,
	.block-title--two.blogzee-dark-mode .widget .wp-block-search__button-outside .wp-block-search__label:after,
	.block-title--two.blogzee-dark-mode .blogzee-you-may-have-missed-section .section-title:after,
	.block-title--two.blogzee-dark-mode .single-related-posts-section .blogzee-block-title:after {
		background-color: #ffffff38;
	}

	.block-title--two .widget_block.widget_search .wp-block-search__label:after {
		top: 14px;
	}

/* block title layout three */
	.block-title--three .widget_block .wp-block-group__inner-container .wp-block-heading:before,
	.block-title--three section.widget .widget-title span:before,
	.block-title--three .widget .wp-block-search__button-outside .wp-block-search__label:before,
	.block-title--three .blogzee-you-may-have-missed-section .section-title:before,
	.block-title--three .single-related-posts-section .blogzee-block-title:before,
	.block-title--three .widget_block .wp-block-group__inner-container .wp-block-heading:after,
	.block-title--three section.widget .widget-title span:after,
	.block-title--three .widget .wp-block-search__button-outside .wp-block-search__label:after,
	.block-title--three .blogzee-you-may-have-missed-section .section-title:after,
	.block-title--three .single-related-posts-section .blogzee-block-title:after {
		content: '';
		height: 2px;
		background-color: var(--blogzee-global-preset-theme-color);
		position: absolute;
		bottom: 4px;
	}

	.block-title--three.blogzee-dark-mode .widget_block .wp-block-group__inner-container .wp-block-heading:before,
	.block-title--three.blogzee-dark-mode section.widget .widget-title span:before,
	.block-title--three.blogzee-dark-mode .widget .wp-block-search__button-outside .wp-block-search__label:before,
	.block-title--three.blogzee-dark-mode .blogzee-you-may-have-missed-section .section-title:before,
	.block-title--three.blogzee-dark-mode .single-related-posts-section .blogzee-block-title:before,
	.block-title--three.blogzee-dark-mode .widget_block .wp-block-group__inner-container .wp-block-heading:after,
	.block-title--three.blogzee-dark-mode section.widget .widget-title span:after,
	.block-title--three.blogzee-dark-mode .widget .wp-block-search__button-outside .wp-block-search__label:after,
	.block-title--three.blogzee-dark-mode .blogzee-you-may-have-missed-section .section-title:after,
	.block-title--three.blogzee-dark-mode .single-related-posts-section .blogzee-block-title:after {
		background-color: #fff;
	}

	.block-title--three section.widget .widget-title span:before,
	.block-title--three .single-related-posts-section .blogzee-block-title:before,
	.block-title--three section.widget .widget-title span:after,
	.block-title--three .single-related-posts-section .blogzee-block-title:after {
		bottom: -7px;
	}

	.block-title--three .widget_block .wp-block-group__inner-container .wp-block-heading:before,
	.block-title--three section.widget .widget-title span:before,
	.block-title--three .widget .wp-block-search__button-outside .wp-block-search__label:before,
	.block-title--three .blogzee-you-may-have-missed-section .section-title:before,
	.block-title--three .single-related-posts-section .blogzee-block-title:before {
		width: 32px;
		left: 0px;
	}

	.block-title--three .widget_block .wp-block-group__inner-container .wp-block-heading:after,
	.block-title--three section.widget .widget-title span:after,
	.block-title--three .widget .wp-block-search__button-outside .wp-block-search__label:after,
	.block-title--three .blogzee-you-may-have-missed-section .section-title:after,
	.block-title--three .single-related-posts-section .blogzee-block-title:after {
		width: 4px;
		left: 36px;
	}

/* block title layout four */
	.block-title--four .widget_block .wp-block-group__inner-container .wp-block-heading,
	.block-title--four .widget_block.widget_search .wp-block-search__label {
		width: initial;
		display: inline-block;
		overflow: visible;
	}

	body.block-title--four section.widget.widget_blogzee_category_collection_widget .widget-title span:before {
		width: 25%;
	}

	body.block-title--four section.widget.widget_blogzee_category_collection_widget .widget-title span:after {
		width: 20%;
	}

	.block-title--four .widget_block .wp-block-group__inner-container .wp-block-heading:before,
	.block-title--four section.widget .widget-title span:before,
	.block-title--four .widget .wp-block-search__button-outside .wp-block-search__label:before,
	.block-title--four .blogzee-you-may-have-missed-section .section-title:before,
	.block-title--four .single-related-posts-section .blogzee-block-title:before,
	.block-title--four .widget_block .wp-block-group__inner-container .wp-block-heading:after,
	.block-title--four section.widget .widget-title span:after,
	.block-title--four .widget .wp-block-search__button-outside .wp-block-search__label:after,
	.block-title--four .blogzee-you-may-have-missed-section .section-title:after,
	.block-title--four .single-related-posts-section .blogzee-block-title:after {
		content: '';
		position: absolute;
		background: #333;    
		height: 1px;
		opacity: .2;
		left: 90%;
		margin-left: 30px;
	}

	.block-title--four .widget_block .wp-block-group__inner-container .wp-block-heading:before,
	.block-title--four section.widget .widget-title span:before,
	.block-title--four .widget .wp-block-search__button-outside .wp-block-search__label:before,
	.block-title--four .blogzee-you-may-have-missed-section .section-title:before,
	.block-title--four .single-related-posts-section .blogzee-block-title:before {
		top: 14px;
		width: 100%;
	}

	.block-title--four .widget_block .wp-block-group__inner-container .wp-block-heading:after,
	.block-title--four section.widget .widget-title span:after,
	.block-title--four .widget .wp-block-search__button-outside .wp-block-search__label:after,
	.block-title--four .blogzee-you-may-have-missed-section .section-title:after,
	.block-title--four .single-related-posts-section .blogzee-block-title:after {
		top: 19px;
		width: 80%;
	}

	.block-title--four section.widget .widget-title span:before,
	.block-title--four .blogzee-you-may-have-missed-section .section-title:before {
		top: 7px;
	}

	.block-title--four section.widget .widget-title span:after,
	.block-title--four .blogzee-you-may-have-missed-section .section-title:after {
		top: 12px;
	}

	.block-title--four .single-related-posts-section .blogzee-block-title:before {
		top: 10px;
	}

	.block-title--four .single-related-posts-section .blogzee-block-title:after {
		top: 15px;
	}

	.wp-block-group__inner-container {
		overflow: hidden;
	}

/* block title layout five */
	body.block-title--five .widget-title,
	body.block-title--five .blogzee-block-title,
	body.block-title--five .blogzee-you-may-have-missed-section .section-title {
		display: flex;
		align-items: center;
		padding-left: 7px;
		gap: 15px;
	}

	body.block-title--five span.divider {
		display: block;
		position: relative;
		border-radius: 50%;
		height: 7px;
		width: 7px;
		flex-shrink: 0;
		color: var(--divider-color);
		background-color: currentColor;
		line-height: initial;
	}

	.block-title--five span.divider:before,
	.block-title--five span.divider:after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 50%;
		border: 1px solid currentColor;
		animation: divider-heading-pulse 2s ease-in-out infinite;
	}

	@keyframes divider-heading-pulse {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		100% {
			transform: scale(3);
			opacity: 0;
		}
	}

/* latest post list */
	ul.wp-block-latest-posts.wp-block-latest-posts__list,
	ol.wp-block-latest-comments,
	ul.wp-block-archives,
	ul.wp-block-categories,
	ul.wp-block-page-list,
	.widget ul.menu,
	.widget.widget_recent_entries ul,
	.widget.widget_categories ul {
		margin: 0;
		padding: 0;
		list-style: none;
	}

	ul.wp-block-latest-posts li,
	ol.wp-block-latest-comments li,
	ul.wp-block-archives li,
	ul.wp-block-categories li,
	ul.wp-block-page-list li,
	.widget ul.menu li,
	.widget.widget_recent_entries ul li,
	.widget.widget_categories ul li {
		border-bottom: 1px solid var(--border-bottom-color-two);
		margin-bottom: 11px;
		padding-bottom: 7px;
		color: var(--blogzee-widget-block-title-color);
	}

	ul.wp-block-latest-posts li:last-child,
	ol.wp-block-latest-comments li:last-child,
	ul.wp-block-archives li:last-child,
	ul.wp-block-categories li:last-child,
	ul.wp-block-page-list li:last-child,
	.widget ul.menu li:last-child,
	.widget ul.menu li:last-child,
	.widget.widget_recent_entries ul li:last-child,
	.widget.widget_categories ul li:last-child {
		border-bottom: none;
		margin-bottom: 0;
		padding-bottom: 0;
	}

	ul.wp-block-latest-posts a,
	ol.wp-block-latest-comments li footer,
	ul.wp-block-archives a,
	ul.wp-block-page-list a,
	.widget ul.menu a,
	.widget.widget_recent_entries ul li a {
		color: var(--blogzee-widget-block-title-color);
		text-decoration: none;
		font-family: var(--blogzee-widget-title-font-family);
		font-style: var(--blogzee-widget-title-font-style);
		font-weight: var(--blogzee-widget-title-font-weight);
		font-size: calc( var(--blogzee-widget-title-font-size) * 1.05);
		line-height: var(--blogzee-widget-title-font-lineheight);
		letter-spacing: var(--blogzee-widget-title-font-letterspacing);
		text-transform: var(--blogzee-widget-title-font-texttransform);
		text-decoration: var(--blogzee-widget-title-font-textdecoration);
	}

	.widget.widget_block .wp-block-archives li a {
		font-family: var(--blogzee-widget-date-font-family);
		font-style: var(--blogzee-widget-content-font-style);
		font-weight: var(--blogzee-widget-date-font-weight);
		font-style: var(--blogzee-widget-date-font-style);
		font-size: var(--blogzee-widget-date-font-size);
		letter-spacing: var(--blogzee-widget-date-font-letterspacing);
		line-height: var(--blogzee-widget-date-font-lineheight);
		text-transform: var(--blogzee-widget-date-font-texttransform);
		text-decoration: var(--blogzee-widget-date-font-textdecoration);
	}

	ul.wp-block-categories a,
	.widget.widget_categories ul li a {
		font-weight: var(--blogzee-widget-category-font-weight);
		font-family: var(--blogzee-widget-category-font-family);
		font-style: var(--blogzee-widget-category-font-style);
		font-size: calc( var(--blogzee-widget-category-font-size) * 1.09 );
		line-height: var(--blogzee-widget-category-font-lineheight);
		letter-spacing: var(--blogzee-widget-category-font-letterspacing);
		text-transform: var(--blogzee-widget-category-font-texttransform);
		text-decoration: var(--blogzee-widget-category-font-textdecoration);
		color: var(--blogzee-widget-block-title-color);
	}

	ol.wp-block-latest-comments li footer a {
		text-decoration: underline;
		color: var(--blogzee-widget-block-title-color);		
		font-family: var(--blogzee-widget-title-font-family);
		font-weight: var(--blogzee-widget-title-font-weight);
		font-style: var(--blogzee-widget-title-font-style);
		font-size: calc( var(--blogzee-widget-title-font-size) * 0.96);
		line-height: var(--blogzee-widget-title-font-lineheight);
		letter-spacing: var(--blogzee-widget-title-font-letterspacing);
	}

	.widget.widget_block .no-comments {
		color: var(--blogzee-widget-block-title-color);
	}

	.wp-block-tag-cloud a {
		color: var(--blogzee-widget-block-title-color);
	}

	/** blogzee default post list */
	.widget_blogzee_post_list_widget .post-item  .post-thumb-image {
		flex: 0 1 30%;
		margin-right: 15px;
	}

	.widget_blogzee_post_list_widget .post-item .post-content-wrap {
		flex: 1 1 70%;
	}

	.widget_tag_cloud .wp-block-tag-cloud {
		margin: 0;
	}

	.widget_blogzee_post_list_widget .post-item .post-content-wrap .post-categories {
		display: none;
	}

/*
=========================================
6.1 Author Widgets
=========================================
*/
	.widget_blogzee_author_info_widget .bmm-author-thumb-wrap {
		display: flex;
		align-items: center;
	}

	.widget_blogzee_author_info_widget .bmm-author-thumb-wrap .post-thumb {
		margin-right: 20px;
		position: relative;
	}

	.widget_blogzee_author_info_widget .bmm-author-thumb-wrap .post-thumb.no-avatar {
		width: 90px;
		height: 90px;
		background-color: #efefef;
	}

	.widget_blogzee_author_info_widget .bmm-author-thumb-wrap .author-elements {
		flex: 1;
	}

	.widget_blogzee_author_info_widget .bmm-author-thumb-wrap .post-thumb img {
		border-radius: 2px;
		width: 90px;
		height: 80px;
		margin: 0 auto;
		object-fit: cover;
		box-shadow: 0px 5px 15px 0px rgb(74 77 84 / 15%);
		--webkit-box-shadow: 0px 5px 15px 0px rgb(74 77 84 / 15%);
	}

	.widget_blogzee_author_info_widget .bmm-author-thumb-wrap .author-elements .author-name {
		font-size: 18px;
		font-family: var(--blogzee-widget-title-font-family);
		line-height: 20px;
		margin: 0 0 3px;
		color: var(--blogzee-post-title-font-color);
	}

	.widget_blogzee_author_info_widget .bmm-author-thumb-wrap .author-elements .author-name a {
		color: inherit;
		text-decoration: none;
	}

	.widget_blogzee_author_info_widget .bmm-author-thumb-wrap .author-elements .author-tag {
		font-family: var(--blogzee-widget-content-font-family);
		font-size: 13px;
		font-weight: 400;
		color: var(--blogzee-post-content-font-color);
		padding-top: 6px;
		display: inline-block;
	}

	.author-content-wrap {
		padding-top: 10px;
	}

	.author-content-wrap .author-desc {
		font-family: var(--blogzee-widget-content-font-family);
		color: var(--blogzee-post-content-font-color);
		font-size: 14px;
		line-height: 26px;
		font-family: var(--blogzee-widget-content-font-family);
	}


/*
=========================================
6.2 Category Widgets
=========================================
*/
	.widget_blogzee_category_collection_widget .categories-wrap .category-item {
		position: relative;
		overflow: hidden;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item + .category-item {
		margin-top: 15px;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item:before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: #00000030;
		z-index: 1;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item img {
		width: 100%;
		height: 70px;
		object-fit: cover;
		box-shadow: 0px 5px 15px 0px rgba(74, 77, 84, 0.33);
		--webkit-box-shadow: 0px 5px 15px 0px rgba(74, 77, 84, 0.33);
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item .cat-meta-wrap {
		width: 100%;
		height: 100%;
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		text-decoration: none;
		z-index: 2;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item .cat-meta.blogzee-post-title {
		display: flex;
		justify-content: space-between;
		height: 100%;
		padding: 0 25px;
		align-items: center;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item .category-name {
		color: #000;
		font-weight: var(--blogzee-widget-category-font-weight);
		font-family: var(--blogzee-widget-category-font-family);
		font-style: var(--blogzee-widget-category-font-style);
		font-size: calc( var(--blogzee-widget-category-font-size) * 0.96 );
		line-height: var(--blogzee-widget-category-font-lineheight);
		letter-spacing: var(--blogzee-widget-category-font-letterspacing);
		text-transform: var(--blogzee-widget-category-font-texttransform);
		text-decoration: var(--blogzee-widget-category-font-textdecoration);
		background-color: #ffffffd9;
		padding: 4px 12px;
		transition: all .3s ease-in-out;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item .icon-count-wrap {
		position: relative;
		overflow: hidden;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item .icon-count-wrap i {
		position: absolute;
		top: 50%;
		left: -30px;
		transform: translateY(-50%);
		color: #FFF;
		transition: all ease-in-out .3s;
		font-size: 14px;
		visibility: hidden;
		opacity: 0;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item:hover .icon-count-wrap i {
		left: 50%;
		transform: translate(-50%, -50%);
		visibility: visible;
		opacity: 1;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item .category-count {
		visibility: visible;
		opacity: 1;
		transition: all ease-in-out .3s;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item:hover .category-count {
		visibility: hidden;
		opacity: 0;
	}

	.widget_blogzee_category_collection_widget .categories-wrap .category-item .category-count {
		display: inline-block;
		color: var(--blogzee-white-text);
		font-weight: var(--blogzee-widget-category-font-weight);
		font-family: var(--blogzee-widget-category-font-family);
		font-style: var(--blogzee-widget-category-font-style);
		font-size: var(--blogzee-widget-category-font-size);
		line-height: var(--blogzee-widget-category-font-lineheight);
		letter-spacing: var(--blogzee-widget-category-font-letterspacing);
		text-transform: var(--blogzee-widget-category-font-texttransform);
		text-decoration: var(--blogzee-widget-category-font-textdecoration);
	}

/*
=========================================
6.3 Grid Widgets
=========================================
*/
	.widget_blogzee_post_grid_widget .post-grid-wrap .post-thumb-image {
		position: relative;
		padding-bottom: calc( 100% * 0.65 );
		background-position: center;
		background-size: cover;
		overflow: hidden;
	}

	.widget_blogzee_post_grid_widget .post-grid-wrap .post-thumb-image.no-feat-img {
		background-color: #efefef;
	}

	.widget_blogzee_post_grid_widget .post-grid-wrap .post-thumb-image img {
		height: 100%;
		width: 100%;
		object-fit: cover;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: scale(1) translate(-50%,-50%);
		box-shadow: 0px 5px 15px 0px rgba(74, 77, 84, 0.33);
		--webkit-box-shadow: 0px 5px 15px 0px rgba(74, 77, 84, 0.33);
	}

	.widget_blogzee_post_grid_widget .post-grid-wrap .post-content-wrap {
		margin-top: 14px;
	}

	.widget_blogzee_post_grid_widget .post-grid-wrap .post-title {
		font-family: var(--blogzee-widget-title-font-family);
		font-weight: var(--blogzee-widget-title-font-weight);
		font-style: var(--blogzee-widget-title-font-style);
		font-size: calc( var(--blogzee-widget-title-font-size) * 1.1 );
		line-height: var(--blogzee-widget-title-font-lineheight);
		letter-spacing: var(--blogzee-widget-title-font-letterspacing);
		margin: 0;
		text-transform: var(--blogzee-widget-title-font-texttransform);
		text-decoration: var(--blogzee-widget-title-font-textdecoration);
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.widget_blogzee_post_grid_widget .post-grid-wrap  .post-title a {
		text-decoration: none;
		font-family: inherit;
		color: var(--blogzee-post-title-font-color);
	}

	.widget_blogzee_post_grid_widget .post-grid-wrap .post-item {
		border-bottom: 1px solid var(--border-bottom-color-two);
		margin-bottom: 15px;
		padding-bottom: 12px;
	}

	.widget_blogzee_post_grid_widget .post-grid-wrap .post-item:last-child {
		border: none;
		margin-bottom: 0;
		padding-bottom: 0;
	}

	.widget_blogzee_post_grid_widget .post-grid-wrap .post-categories {
		position: absolute;
		left: 10px;
		top: 10px;
		width: auto;
		margin: 0;
		list-style: none;
		padding: 0;
		z-index: 2;
	}

	.widget_blogzee_post_grid_widget .post-grid-wrap .post-categories a {
		padding: 2px 12px;
		color: var(--blogzee-widget-category-font-color);
		font-weight: var(--blogzee-widget-category-font-weight);
		font-family: var(--blogzee-widget-category-font-family);
		font-style: var(--blogzee-widget-category-font-style);
		font-size: calc( var(--blogzee-widget-category-font-size) * 0.92 );
		line-height: var(--blogzee-widget-category-font-lineheight);
		letter-spacing: var(--blogzee-widget-category-font-letterspacing);
		text-transform: var(--blogzee-widget-category-font-texttransform);
		text-decoration: var(--blogzee-widget-category-font-textdecoration);
	}

/*
=========================================
6.4 List Widgets
=========================================
*/
	.widget_blogzee_post_list_widget .post-item {
		display: flex;
		align-items: center;
	}

	.widget_blogzee_post_list_widget .post-list-wrap .post-thumb-image {
		position: relative;
		padding-bottom: 25%;
		background-position: center;
		background-size: cover;
		overflow: hidden;
	}

	.widget_blogzee_post_list_widget .post-list-wrap .post-thumb-image.no-feat-img {
		background-color: #efefef;
	}

	.widget_blogzee_post_list_widget .post-list-wrap .post-thumb-image img {
		height: 101%;
		width: 100%;
		object-fit: cover;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: scale(1) translate(-50%,-50%);
	}

	.widget_blogzee_post_list_widget .post-list-wrap .post-categories a {
		background: transparent;
		color: var(--blogzee-widget-block-title-color);
		padding: 0;
		font-size: 13px;
		font-weight: 500;
	}

	.widget_blogzee_post_list_widget .post-list-wrap .post-title {
		font-family: var(--blogzee-widget-title-font-family);
		font-weight: var(--blogzee-widget-title-font-weight);
		font-size: calc( var(--blogzee-widget-title-font-size) * 1.1);
		line-height: var(--blogzee-widget-title-font-lineheight);
		letter-spacing: var(--blogzee-widget-title-font-letterspacing);
		font-style: var(--blogzee-widget-title-font-style);
		color: var(--blogzee-post-title-font-color);
		text-transform: var(--blogzee-widget-title-font-texttransform);
		text-decoration: var(--blogzee-widget-title-font-textdecoration);
		margin: 2px 0 5px 0px;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.widget_blogzee_post_list_widget .post-list-wrap  .post-title a {
		text-decoration: none;
		font-family: inherit;
		color: inherit;
	}

	.widget_blogzee_post_list_widget .post-list-wrap .post-item {
		border-bottom: 1px solid var(--border-bottom-color-two);
		margin-bottom: 15px;
		padding-bottom: 12px;
	}

	.widget_blogzee_post_list_widget .post-list-wrap .post-item:last-child {
		border: none;
		margin-bottom: 0;
		padding-bottom: 0;
	}

	.widget li.wp-block-latest-comments__comment article {
		width: 100%;
		margin: 0;
		padding-bottom: 5px;
	}

	.widget li.wp-block-latest-comments__comment article a {
		text-decoration: none;
	}

	ol.wp-block-latest-comments li footer a.wp-block-latest-comments__comment-author {
		font-size: 15px;
	}

	ol.wp-block-latest-comments li footer a.wp-block-latest-comments__comment-link {
		text-decoration: underline;
		font-style: italic;
	}

	.widget .post-meta .post-date {
		font-family: var(--blogzee-widget-date-font-family);
		font-style: var(--blogzee-widget-content-font-style);
		font-weight: var(--blogzee-widget-date-font-weight);
		font-style: var(--blogzee-widget-date-font-style);
		font-size: calc( var(--blogzee-widget-date-font-size) * 0.9 );
		letter-spacing: var(--blogzee-widget-date-font-letterspacing);
		line-height: var(--blogzee-widget-date-font-lineheight);
		text-transform: var(--blogzee-widget-date-font-texttransform);
		text-decoration: var(--blogzee-widget-date-font-textdecoration);
	}

	.widget .post-meta .post-date i {
		font-size: calc( var(--blogzee-widget-date-font-size) * 0.78 );
	}

	.widget .post-meta .post-date {
		margin-bottom: 0;
	}

	.widget_blogzee_post_list_widget .post-list-wrap.layout--two .post-item:first-child {
		display: inline-block;
		width: 100%;
	}

	.widget_blogzee_post_list_widget .post-list-wrap.layout--two .post-item:first-child .post-thumb-image {
		width: 100%;
		height: 100%;
		padding-bottom: 60%;
		margin: 0;
	}

	.widget_blogzee_post_list_widget .post-list-wrap.layout--two .post-item:first-child .post-content-wrap {
		margin-top: 14px;
	}

	aside .widget_blogzee_post_list_widget .post-list-wrap.layout--two .post-item:first-child .post-title,
	body .widget_blogzee_post_grid_widget .post-grid-wrap .post-title,
	body .widget_blogzee_carousel_widget .post-title {
		font-size: calc( var(--blogzee-widget-title-font-size) * 1.3);
		line-height: calc( var(--blogzee-widget-title-font-lineheight) * 1.2 );
		margin: 14px 0 6px;
	}

/*
=========================================
6.5 Social Share
=========================================
*/

	/* social share */
	section.widget_blogzee_social_platforms_widget .widget-title {
		margin-bottom: 18px;
	}

	.widget.widget_blogzee_social_platforms_widget a {
		color: var(--blogzee-post-title-font-color);
		margin: 0 4px;
	}

	.widget.widget_blogzee_social_platforms_widget .blogzee-social-icon.official-color--enabled a i {
		color: #fff;
	}

	/* OFFICIAL COLOR */
	.social-platforms-widget .blogzee-social-icon {
		display: flex;
		gap: 15px;
		flex-wrap: wrap;
	}

	.widget.widget_blogzee_social_platforms_widget .global-color-icon a {
		margin: 0;
	}

	.social-platforms-widget.global-color-icon i {
		background-color: var(--blogzee-global-preset-theme-color);
		color: var(--blogzee-white-text);
		box-shadow: 0 5px 10px -2px var(--blogzee-global-preset-theme-color);
		padding: 10px;
		font-size: 20px;
		border-radius: 4px;
		display: inline-block;
		width: 44px;
		text-align: center;
	}

	
/*
=========================================
6.6 Tags Collection
=========================================
*/
	.widget_blogzee_tags_collection_widget .tags-wrap .tags-item{
		background-color: var(--blogzee-global-preset-theme-color);
		padding: 6px 12px;
		border-radius: 6px;
		color: var(--blogzee-white-text);
		text-decoration: none;
		font-family: var(--blogzee-category-font-family);
		font-style: var(--blogzee-category-font-style);
		font-size: var(--blogzee-category-font-size);
		letter-spacing: 0.5px;
		font-weight: var(--blogzee-category-font-weight);
		display: inline-block;
		margin-bottom: 10px;
		margin-right: 5px;
		line-height: 19px;
	}

	.tag-meta-wrap {
		text-decoration: none;
	}

	.tags-wrap .tag-meta.blogzee-post-title {
		color: var(--blogzee-white-text);
	}

	.tags-wrap .tags-item:hover {
		cursor: pointer;
	}

	.tags-wrap .tags-item .tags-count {
		display: inline-block;
		font-size: 12px;
		padding: 5px;
		border-radius: 50%;
		background: #ffffff4d;
		width: 22px;
		height: 22px;
		text-align: center;
		line-height: 12px;
		margin-left: 10px;
	}

/* addinal widget design **/
.widget .sub-menu {
	list-style: none;
	margin-left: 0;
	padding-left: 15px;
	padding-top: 10px;
}

/*
=========================================
6.8 Grid Two Column
=========================================
*/

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 15px;
	}

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap .blaze_box_wrap {
		overflow: hidden;
	}

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-thumb {
		overflow: hidden;
		height: 110px;
		position: relative;
	}

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap .post_thumb_image.no-feat-img {
		background-color: #efefef;
	}

	.widget_blogzee_posts_grid_two_column_widget .post-categories .cat-item a {
		padding: 1px 10px;
	}

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-thumb img {
		height: 100%;
		width: 100%;
		object-fit: cover;
	}

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap .bmm-post-cats-wrap {
		position: absolute;
		top: 10px;
		left: 10px;
		z-index: 2;
	}

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap .bmm-post-cats-wrap .card__content-category {
		margin: 0;
	}

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap .bmm-post-cats-wrap .card__content-category a {
		font-size: calc(var(--blogzee-category-font-size)* 0.7);
		line-height: calc(var(--blogzee-category-font-lineheight)* 0.88);
	}

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-title {
		font-family: var(--blogzee-widget-title-font-family);
		font-weight: var(--blogzee-widget-title-font-weight);
		font-style: var(--blogzee-widget-title-font-style);
		font-size: var(--blogzee-widget-title-font-size);
		line-height: var(--blogzee-widget-title-font-lineheight);
		letter-spacing: var(--blogzee-widget-title-font-letterspacing);
		text-transform: var(--blogzee-widget-title-font-texttransform);
		margin: 10px 0 5px 0px;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-title a {
		text-decoration: none;
		color: var(--blogzee-post-title-font-color);
	}

/*
=========================================
6.9 Carousel Widget
=========================================
*/

	.widget_blogzee_carousel_widget .swiper-arrow {
		font-size: 12px;
		width: 35px;
		height: 40px;
		line-height: 40px;
		margin-top: -30px;
	}

	.widget_blogzee_carousel_widget .swiper-vertical .swiper-arrow {
		transform: rotate(90deg);
		padding: 8px 9px;
	}

	.widget_blogzee_carousel_widget .swiper.custom-button-prev {
		right: 45px;
	}

	.widget_blogzee_carousel_widget .swiper-vertical .swiper-arrow.custom-button-prev {
		right: 48px;
	}

	.widget_blogzee_carousel_widget .swiper-arrow.custom-button-next {
		right: 15px;
	}

	body .widget.widget_blogzee_carousel_widget .blogzee-widget-carousel-posts .carousel-posts-wrap .slick-list .slick-track article + article {
		padding: 0 !important;
		border: none !important;
	}

	@media (max-width: 1320px) {
		.widget_blogzee_carousel_widget .swiper-vertical .post-item {
			min-height: 345px;
			overflow: hidden;
		}
	}

	.widget_blogzee_carousel_widget .post-item img {
		height: 100%;
		width: 100%;
		object-fit: cover;
	}

	.widget_blogzee_carousel_widget .post-thumb-wrap {
		position: relative;
		overflow: hidden;
		height: 208px;
	}

	.widget_blogzee_carousel_widget .swiper-vertical .post-item {
		height: 300px !important;
	}

	.widget_blogzee_carousel_widget .post-thumb-wrap .post-categories {
		position: absolute;
		top: 15px;
		left: 15px;
		z-index: 2;
		list-style: none;
		padding: 0;
		margin: 0;
	}

	.widget_blogzee_carousel_widget .post-thumb-wrap .post-categories li {
		display: inline-block;
		margin-right: 10px;
	}

	.widget_blogzee_carousel_widget .post-title {
		font-family: var(--blogzee-widget-title-font-family);
		font-weight: var(--blogzee-widget-title-font-weight);
		font-style: var(--blogzee-widget-title-font-style);
		font-size: calc(var(--blogzee-widget-title-font-size)* 1.1);
		line-height: var(--blogzee-widget-title-font-lineheight);
		letter-spacing: var(--blogzee-widget-title-font-letterspacing);
		text-transform: var(--blogzee-widget-title-font-texttransform);
		margin: 5px 0 8px 0px;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.widget_blogzee_carousel_widget .post-title a {
		color: var(--blogzee-post-title-font-color);
		text-decoration: none;
	}

	.widget_blogzee_carousel_widget .post-meta {
		margin: 0;
	}

	.widget_blogzee_carousel_widget .post-meta .post-date i {
		padding-left: 0;
	}
	
/*
=========================================
7.0 Pagination & Ajax Button
=========================================
*/
.navigation.posts-navigation {
	margin: 40px 0 0 0;
}

.posts-navigation .nav-previous a,
.posts-navigation .nav-next a {
	display: inline-block;
	line-height: 1;
	text-decoration: none;
	font-family: var(--blogzee-readmore-font-family);
	font-size: 15px;
	font-weight: var(--blogzee-readmore-font-weight);
	color: var(--blogzee-post-title-font-color);
	display: flex;
	align-items: center;
}

.blogzee-light-mode .posts-navigation .nav-links a:hover {
	color: var(--blogzee-global-preset-theme-color);
}

.posts-navigation .nav-previous a:before {
	content: '\f100';
	margin-right: 12px;
}

.posts-navigation .nav-next a:after {
	content: '\f101';
	margin-left: 12px;
}

.posts-navigation .nav-previous a:before,
.posts-navigation .nav-next a:after {
	font-family: 'Font Awesome 5 Free';
	font-size: 12px;
	display: inline-block;
	font-weight: 900;
}

#blogzee-main-wrap .pagination {
	display: flex;
	justify-content: center;
	margin-top: 40px;
	align-items: center;
}

#blogzee-main-wrap ul.page-numbers {
	margin-left: 0;
	margin: 0;
	background: var(--blogzee-white-dark-color);
    padding: 10px 30px;
    border-radius: 6px;
	box-shadow: 0px 0 20px 0px rgb(0 0 0 / 3%);
}

#blogzee-main-wrap ul.page-numbers li {
	display: inline-block;
}

#blogzee-main-wrap ul.page-numbers li + li {
	margin-left: 26px;
}

#blogzee-main-wrap .pagination span.page-numbers,
#blogzee-main-wrap .pagination a.page-numbers {
	font-size: 16px;
	display: inline-block;
	text-align: center;
	text-decoration: none;
	font-family: var(--blogzee-post-title-font-family);
	color: var(--blogzee-archive-pagination-color);
	font-weight: 600;
}

#blogzee-main-wrap .pagination span.current,
#blogzee-main-wrap .pagination li:hover a {
	color: var(--blogzee-global-preset-theme-color);
}

#blogzee-main-wrap .pagination i {
	font-size: 15px;
}

input.wpcf7-submit {
	text-decoration: none;
	padding: 10px 25px;
	background: var(--blogzee-custom-button-bk-color);
	border-radius: var(--blogzee-custom-button-border-radius);
	border: none;
	text-transform: var(--blogzee-custom-button-texttransform);
	text-decoration: var(--blogzee-custom-button-textdecoration);
	font-weight: var(--blogzee-custom-button-weight);
	box-shadow: 0 0 3px 1px rgb(224 224 224 / 12%);
	-webkit-box-shadow: 0 0 3px 1px rgb(224 224 224 / 12%);
	color: #fff;
}

input.wpcf7-submit:hover {
	cursor: pointer;
}

.pagination.pagination-type--ajax-load-more {
	margin-top: 40px;
	pointer-events: none;
}

.pagination span.loader {
	width: 20px;
    height: 20px;
	display: inline-block;
    border-radius: 50%;
    padding: 1px;
    background: conic-gradient(#0000 10%, #fff) content-box;
    -webkit-mask: repeating-conic-gradient(#0000 0deg, #000 1deg 20deg, #0000 21deg 36deg), radial-gradient(farthest-side, #0000 calc(100% - 11px), #000 calc(100% - 5px));
    -webkit-mask-composite: destination-in;
    mask-composite: intersect;
    animation: loader-rotate 1s infinite steps(10);
	display: none;
}

.pagination.retrieving-posts span.loader {
	display: block;
}

@keyframes loader-rotate {
	100% {
		transform: rotate(1turn);
	}
}

.ajax-load-more-wrap {
	color: var(--blogzee-ajax-pagination-color);
	display: inline-block;
}

#blogzee-main-wrap .ajax-load-more-wrap .pagination-icon {
	color: var(--blogzee-ajax-pagination-color);
	border: none;
	width: auto;
	height: auto;
	line-height: 1;
}

#blogzee-main-wrap .pagination .ajax-load-more-wrap {
	background: var(--blogzee-ajax-pagination-bk-color);
	padding: 12px 20px;
	border-radius: 2px;
	pointer-events: all;
	display: flex;
    align-items: center;
    gap: 12px;
}

#blogzee-main-wrap .pagination .ajax-load-more-wrap:hover {
	background: var(--blogzee-ajax-pagination-bk-color-hover);
}

#blogzee-main-wrap .pagination .ajax-load-more-wrap:hover,
#blogzee-main-wrap .pagination .ajax-load-more-wrap:hover .pagination-icon {
	color: var(--blogzee-ajax-pagination-color-hover);
	cursor: pointer;
	border: none;
}

#blogzee-main-wrap .pagination .ajax-load-more-wrap .button-label {
	margin: 0;
	display: inline-block;
	font-size: 16px;
	font-family: var(--blogzee-readmore-font-family);
	font-weight: var(--blogzee-readmore-font-weight);
	font-style: var(--blogzee-readtime-font-style);
	letter-spacing: var(--blogzee-readmore-font-letterspacing);
	line-height: var(--blogzee-readmore-font-lineheight);
	text-transform: var(--blogzee-readmore-font-texttransform);
	text-decoration: var(--blogzee-readmore-font-textdecoration);
}

#blogzee-main-wrap .pagination .ajax-load-more-wrap img {
	width: 24px;
	vertical-align: middle;
}

#blogzee-main-wrap .pagination .pagination-icon.icon-context--before {
	margin: 0;
	margin-right: 15px;
}

#blogzee-main-wrap .pagination div.no-more-posts {
	width: 100%;
}

#blogzee-main-wrap .pagination span.no-more-posts  {
	border: none;
	width: auto;
	text-align: center;
	font-family: var(--blogzee-custom-button-family);
	font-weight: 500;
	letter-spacing: 0.3px;
	box-shadow: none;
}

.blogzee-widget-loader {
	text-align: center;
	overflow: hidden;
}

.widget {
	position: relative;
}

.blogzee-carousel-section .carousel-wrap:not(.swiper-initialized):before,
.widget.retrieving-posts:before {
	content: '';
    position: absolute;
    margin: auto;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 15px;
    height: 15px;
    border-radius: 100%;
    z-index: 2;
    background-color: transparent;
    box-shadow: 15px 15px #55dbdb, -15px 15px #de8a0d, -15px -15px #55dbdb, 15px -15px #de8a0d;
    -o-box-shadow: 15px 15px #55dbdb, -15px 15px #de8a0d, -15px -15px #55dbdb, 15px -15px #de8a0d;
    -ms-box-shadow: 15px 15px #55dbdb, -15px 15px #de8a0d, -15px -15px #55dbdb, 15px -15px #de8a0d;
    -webkit-box-shadow: 15px 15px #55dbdb, -15px 15px #de8a0d, -15px -15px #55dbdb, 15px -15px #de8a0d;
    -moz-box-shadow: 15px 15px #55dbdb, -15px 15px #de8a0d, -15px -15px #55dbdb, 15px -15px #de8a0d;
    animation: cssload-spin ease infinite 4.6s;
    -o-animation: cssload-spin ease infinite 4.6s;
    -ms-animation: cssload-spin ease infinite 4.6s;
    -webkit-animation: cssload-spin ease infinite 4.6s;
    -moz-animation: cssload-spin ease infinite 4.6s;
}

@keyframes cssload-spin {
	0%, 100% {
		box-shadow: 15px 15px #55dbdb, -15px 15px #de8a0d, -15px -15px #55dbdb, 15px -15px #de8a0d;
	}
	25% {
		box-shadow: -15px 15px #de8a0d, -15px -15px #55dbdb, 15px -15px #de8a0d, 15px 15px #55dbdb;
	}
	50% {
		box-shadow: -15px -15px #55dbdb, 15px -15px #de8a0d, 15px 15px #55dbdb, -15px 15px #de8a0d;
	}
	75% {
		box-shadow: 15px -15px #de8a0d, 15px 15px #55dbdb, -15px 15px #de8a0d, -15px -15px #55dbdb;
	}
}

.widget.retrieving-posts .post-item {
	opacity: 0.3;
}

.blogzee-widget-loader .load-more {
	text-decoration: none;
	border: none;
	background: var(--blogzee-widget-btn-bk-color);
	color: var(--blogzee-widget-btn-color);
	text-transform: none;
	text-decoration: none;
	font-weight: 500;
	margin-top: 25px;
	margin-bottom: 2px;
	font-size: 13px;
	cursor: pointer;
	position: relative;
	font-family: var(--blogzee-custom-button-family);
	transition: all 1s;
	overflow: hidden;
	z-index: 1;
}

.blogzee-widget-loader .load-more:after {
    background-color: var(--blogzee-widget-btn-bk-color-hover);
}

.blogzee-widget-loader .load-more:hover {
	color: var(--blogzee-widget-btn-color-hover);
}

body .canvas-menu-sidebar .blogzee-widget-loader .load-more {
	background-color: transparent;
	box-shadow: 0px 0px 0px 1px #d7d7d780;
	color: var(--blogzee-post-title-font-color);
}

/*
=========================================
8.0 Post format
=========================================
*/

.tax-post_format #blogzee-main-wrap .page-header .page-title {
	margin: 15px 0 20px;
}

/** Gallery Post Format **/
	article.post_format-post-format-gallery .swiper-arrow {
		width: 35px;
		height: 40px;
		line-height: 39px;
	}

	article.post_format-post-format-gallery .swiper-arrow i {
		line-height: 1.1;
		vertical-align: middle;
		margin-top: 1px;
		margin-bottom: 1px;
		color: #e4e4e4;
		font-size: 11px;
	}

	article.post_format-post-format-gallery .swiper-arrow:hover {
		cursor: pointer;
	}

	body article.post_format-post-format-gallery figure.post-thumbnail-wrapper .slick-list img {
		height: 100%; 
		width: 100%; 
		object-fit: cover; 
		position: absolute!important;
		top: 50%!important;
		left: 50%!important;
		transform: scale(1) translate(-50%,-50%);
	}

	body article.post_format-post-format-gallery figure.post-thumbnail-wrapper .slick-list img:hover {
		cursor: e-resize;
	}

	body #blogzee-main-wrap > .blogzee-container > .row #primary article.post_format-post-format-gallery figure .post-categories {
		z-index: 999;
	}

/** Image Post Format **/

	article.post_format-post-format-image .post-meta .byline img {
		width: 20px;
		height: 20px;
		object-fit: initial;
		position: relative;
		top: initial;
		left: initial;
		transform: scale(1);
	}

	#blogzee-main-wrap > .blogzee-container > .row #primary article .post-thumbnail-wrapper .inner-content { 
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 100%;
		padding: 20px;
		z-index: 2;
	}

	article.post_format-post-format-image .post-thumbnail-wrapper .inner-content,
	article.post_format-post-format-image .post-thumbnail-wrapper .entry-title,
	article.post_format-post-format-image .post-thumbnail-wrapper .post-excerpt,
	article.post_format-post-format-image .post-thumbnail-wrapper .post-meta .byline,
	article.post_format-post-format-image .post-thumbnail-wrapper .post-meta,
	article.post_format-post-format-image .post-thumbnail-wrapper .post-date i { 
		color: #fff;
	}

	body.archive-mobile-column--two article.post_format-post-format-image figure.post-thumbnail-wrapper  .post-thumnail-inner-wrapper:after {
		content: '';
		height: 100%;
		width: 100%;
		position: absolute;
		left: 0;
		top: 0;
		border-radius: 15px;
		overflow: hidden;
		background-color: #0034ff25;
	}

	body .blogzee-main-wrap #primary article figure.post-thumbnail-wrapper .content-wrap {
		max-height: 230px;
		overflow-y: auto;
	}

/** Audo Post Format **/
	body:not(.single) article.post_format-post-format-audio audio {
		width: calc(100% - 40px);
		height: 45px;
		position: absolute;
		left: 20px;
		bottom: 20px;
		z-index: 2;
	}

	body.archive--grid-two-layout article.post_format-post-format-audio audio {
		width: 100%;
		position: initial;
		margin-top: 18px;
	}

	body.single .wp-block-embed-soundcloud iframe {
		width: 100%;
	}

/** Video Post Format **/
	article.post_format-post-format-video .wp-block-video video {
		width: 100%;
		height: 101%;
		object-fit: cover;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: scale(1) translate(-50%,-50%);
	}

	article.post_format-post-format-video .playing .post-thumnail-inner-wrapper {
		z-index: 9;
	}
	
	article.post_format-post-format-video .video-overlay {
		height: 100%;
		background: transparent;
		z-index: 1;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		cursor: pointer;
	}
	
	article.post_format-post-format-video .inner-content {
		transition: all .3s ease-in-out;
	}
	
	body.archive--grid-two-layout #primary article.post_format-post-format-video .playing .inner-content {
		margin-top: -10px;
	
	}

	body.single .wp-block-embed-youtube iframe {
		width: 100%;
	}

/** Quote Post Format **/
	article.post_format-post-format-quote .post-meta .byline img {
		object-fit: initial;
		position: relative;
		top: initial;
		left: initial;
		transform: scale(1);
	}

	article.post_format-post-format-quote .post-thumbnail-wrapper .inner-content,
	article.post_format-post-format-quote .post-thumbnail-wrapper .entry-title,
	article.post_format-post-format-quote .post-thumbnail-wrapper .post-excerpt,
	article.post_format-post-format-quote .post-thumbnail-wrapper .post-meta .byline,
	article.post_format-post-format-quote .post-thumbnail-wrapper .post-meta { 
		color: #fff;
	}

	body.archive-mobile-column--two article.post_format-post-format-quote figure.post-thumbnail-wrapper  .post-thumnail-inner-wrapper:after {
		content: '';
		height: 100%;
		width: 100%;
		position: absolute;
		left: 0;
		top: 0;
		border-radius: 15px;
		overflow: hidden;
		background-color: #0034ff25;
	}

	article.post_format-post-format-quote .wp-block-quote {
		font-family: var(--blogzee-post-content-font-family);
		font-style: var(--blogzee-post-content-font-style);
		font-size: var(--blogzee-post-content-font-size);
		color: var(--blogzee-white-text);
		font-weight: var(--blogzee-post-content-font-weight);
		line-height: var(--blogzee-post-content-font-lineheight);
		letter-spacing: var(--blogzee-post-content-font-letterspacing);
	}

	article.format-quote blockquote.wp-block-quote:before {
		content: "\f10d";
		padding: initial;
		font-family: 'Font Awesome 6 Free';
		font-weight: 900;
		position: absolute;
		top: calc( 50% - 50px);
		opacity: 0.5;
		font-size: 110px;
		left: calc(50% - 50px);
		color: var(--blogzee-global-preset-theme-color);
		opacity: 0.2;
		z-index: -1;
	}

	article.format-quote .post-button {
		margin-top: 12px;
	}

	blockquote.wp-block-quote p {
		margin-bottom: 10px;
		margin-top: 0;
	}

	blockquote cite {
		font-weight: 700;
	}

	article.post_format-post-format-quote .post-meta a.url{
		text-decoration: none;
		color: inherit;
	}

	article.format-quote .content-wrap::-webkit-scrollbar {
		width: 2px;
	}

/*
=========================================
9.0 Single Posts
=========================================
*/

body.single-post #primary article.format-video figure.wp-block-video video {
	position: relative;
	transform: none;
	left: 0;
	top: 0;
	width: 100%;
	height: auto;
}

body.single #primary .blogzee-inner-content-wrap .wp-block-quote {
	position: relative;
}

body.single #primary .blogzee-inner-content-wrap .wp-block-quote {
	background-color: #F9F9FF;
	padding: 80px 25px 20px;
	border-radius: 2px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin: 0;
	text-align: center;
}

body.single #primary .blogzee-inner-content-wrap .wp-block-quote:before {
	content: '\f10d';
	position: absolute;
	left: 50%;
	top: 25px;
	transform: translateX(-50%);
	background: var(--blogzee-global-preset-theme-color);
	font-family: 'Font Awesome 5 Free';
	font-weight: 900;
	color: #fff;
	border-radius: 50%;
	padding: 0;
	width: 35px;
	height: 35px;
	line-height: 35px;
	font-size: 18px;
}

body.single #primary .blogzee-inner-content-wrap .wp-block-quote strong em {
	position: relative;
	padding-left: 30px;
}

body.single #primary .blogzee-inner-content-wrap .wp-block-quote strong em:before {
	content: '';
	position: absolute;
	border-top: 2px solid;
	width: 15px;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	background: #002b41;
}

body.single-post #primary .blogzee-inner-content-wrap article .entry-content .wp-block-latest-posts a, 
body.single-post #primary .blogzee-inner-content-wrap article .entry-content .wp-block-categories a {
	color: #333;
	text-decoration: none;
	color: initial;
}

body.single-post #primary .blogzee-inner-content-wrap article .entry-content .wp-block-button a {
	color: #fff;
}

body.single-post #blogzee-main-wrap .blogzee-container .row #primary article header.entry-header .post-categories {
	position: absolute;
	left: 20px;
	top: 15px;
	z-index: 10;
	text-align: center;
	margin: 0;
	padding: 0;
	list-style: none;
	text-decoration: none;
	display: flex;
	flex-wrap: wrap;
}

body.single-post #blogzee-main-wrap #primary .post-categories li{
	margin-right: 10px;
	display: inline-block;
}

body.single-post #blogzee-main-wrap .blogzee-container .row #primary .post-inner,
body.single-post #blogzee-main-wrap .blogzee-container .row #primary .comments-area,
body.single-post #primary article .post-card .bmm-author-thumb-wrap,
body.single-post #blogzee-main-wrap .blogzee-container .row #primary nav.navigation,
body.single-post #blogzee-main-wrap .blogzee-container .row #primary .comments-area,
body.single-post #blogzee-main-wrap .blogzee-container .row #primary .single-related-posts-section-wrap.layout--list {
	padding: 25px;
	overflow: hidden;
}

body.single-post #blogzee-main-wrap .blogzee-container .row #primary .post-inner,
body.single-post #blogzee-main-wrap .blogzee-container .row #primary .comments-area {
	margin-bottom: 35px;
}

body.single-post #blogzee-main-wrap .blogzee-container .row #primary nav.navigation {
	margin-bottom: 35px;
	padding: 15px;
}

body.single-post #blogzee-main-wrap .blogzee-container .row #primary .post-thumbnail {
	overflow: hidden;
}

body.single-post #blogzee-main-wrap .blogzee-container .row #primary .post-thumbnail img {
	width: 100%;
	display: block;
}

body.single-post #blogzee-main-wrap .blogzee-container .row #primary .blogzee-inner-content-wrap > article header {
	position: relative;
}

.no-single-featured-image.post-thumbnail {
	background-color: #00000040;
}

body.single-post .post-meta-wrap {
	line-height: 1;
	font-size: 14px;
	font-weight: 500;
	letter-spacing: 0.3px;
	align-items: center;
}

body.single-post .post-meta-wrap .byline {
	flex: 0 1 30%;
	text-align: left;
	vertical-align: middle;
	font-family: "Montserrat",sans-serif;
	font-weight: 500;
	font-size: 15px;
	letter-spacing: 0.3px;
	line-height: 22px;
	display: inline-block;
	color: var(--blogzee-meta-font-color);
	margin-right: 15px;
}

body.single-post .post-meta-wrap .byline a {
	text-decoration: none;
	color: inherit;
	font-size: inherit;
}

body.single-post .post-meta-wrap .byline img {
	vertical-align: middle;
	border-radius: 50%;
	margin-right: 10px;
	margin-top: -2px;
	width: 24px;
	height: 24px;
}

body.single-post .post-meta-wrap .post-meta {
	font-family: var(--blogzee-meta-font-family);
	font-size: var(--blogzee-meta-font-size);
	font-weight: var(--blogzee-meta-font-weight);
	color: var(--blogzee-meta-font-color);
}

body.single-post .post-meta-wrap .post-meta a {
	text-decoration: none;
	color: inherit;
}

body.single-post .post-meta-wrap .post-meta .post-comments-num {
	margin-left: 15px;
	display: inline-block;
}

body.single-post .post-meta-wrap .post-meta .post-read-time {
	display: inline-block;
}

body.single-post .post-meta-wrap .post-meta .post-button {
	display: inline-block;
	background-color: var(--blogzee-global-preset-theme-color);
	padding: 9px 20px;
	border-radius: 15px;
	color: #fff;
	margin-left: 10px;
	line-height: 1;
	box-shadow: 0 0 3px 1px rgb(224 224 224 / 25%);
	-webkit-box-shadow: 0 0 3px 1px rgb(224 224 224 / 25%);
}

body.single-post .post-meta-wrap .post-meta .post-button i{
	padding: 0;
	color: inherit;
}

body.single-post .post-meta-wrap .post-meta .post-button a {
	color: inherit;
}

body.single-post.post_format-post-format-gallery .post-thumbnail-wrapper {
	display: grid;
}

body.single-post article p:first-child {
	margin-top: 0;
}

body.single-post article p:last-child {
	margin-bottom: 0;
}

body.single-post article p.logged-in-as {
	margin-bottom: 10px;
}

body.single-post article .entry-content p a,
body.single-post article .entry-content a {
	color: var(--blogzee-global-preset-theme-color);
	text-decoration: none;
}

body.single-post .post-meta-wrap .post-meta i {
	padding-left: 5px;
	padding-right: 6px;
}

body.single-post article .entry-content {
	color: var(--blogzee-single-content-color);
}

body.single-post.single-post--layout-five article .entry-content {
	margin-top: 0;
}

body.single-post article .entry-content p {
	font-size: inherit;
	line-height: inherit;
	letter-spacing: inherit;
	margin-bottom: 15px;
}

body.single-post article .entry-content.content-alignment--center p {
	text-align: center;
}

body.single-post article .entry-content.content-alignment--right p {
	text-align: right;
}

body.single-post article.format-image .post-date {
	color: var(--blogzee-meta-font-color);
}

body.single-post .single-header-content-wrap {
	margin-top: 4px;
}

body.single-post .blogzee-single-header  .single-header-content-wrap .post-categories {
	margin: 4px 0;
}

body.single-post .single-header-content-wrap .entry-title {
	margin: 8px 0;
}

body.single-post .single-header-content-wrap .post-meta-wrap {
	margin: 8px 0;
}

body.single-post .entry-content ol,
body.single-post .entry-content ul {
	padding: 0;
}

body.single-post .entry-content ol li,
body.single-post .entry-content ul li {
	padding: 3px 0;
}

/* Single Two */
	body.single-post .single-header-content-wrap .post-meta-wrap {
		margin: 10px 0;
	}

/* Related Posts */
	.single-related-posts-section-wrap .single-related-posts-wrap {
		display: grid;
		gap: 20px;
	}

	.single-related-posts-section-wrap.column--four .single-related-posts-wrap {
		grid-template-columns: repeat(4, 1fr);
	}

	.single-related-posts-section-wrap.column--three .single-related-posts-wrap {
		grid-template-columns: repeat(3, 1fr);
	}

	.single-related-posts-section-wrap.column--two .single-related-posts-wrap {
		grid-template-columns: repeat(2, 1fr);
	}

	.single-related-posts-section-wrap.column--one .single-related-posts-wrap {
		grid-template-columns: 100%;
	}

	body.single .single-related-posts-section-wrap .single-related-posts-wrap .post-element .post-meta > span,
	body.single .single-related-posts-section-wrap .single-related-posts-wrap .post-element .post-comments-num {
		font-size: 13px;
		line-height: 18px;
	}

/* Related Post layout two **/
	.single-related-posts-section-wrap.layout--list.layout--two.column--two .post-thumbnail {
		padding-bottom: 50%;
	}

	.single-related-posts-section-wrap.layout--list.layout--two .post-thumbnail {
		padding-bottom: 60%;
	}

	.single-related-posts-section-wrap.layout--list.layout--two .single-related-posts-wrap > article > figure,
	.single-related-posts-section-wrap.layout--list.layout--two .single-related-posts-wrap > article > div {
		width: 100%;
	}

	.single-related-posts-section-wrap.layout--list.layout--two .single-related-posts-wrap article .post-thumb-wrap {
		margin: 0;
	}

	.single-related-posts-section-wrap.layout--list.layout--two .single-related-posts-wrap article .post-element {
		flex: none;
	}

	body .single-related-posts-section-wrap.layout--list .single-related-posts-wrap article .post-element .post-title {
		font-size: 1.19rem;
		margin: 12px 0 7px;
		line-height: 30px;
	}

	.single-related-posts-section-wrap.layout--list.column--three .single-related-posts-wrap article .post-element .post-title {
		font-size: 1.04rem;
		line-height: 27px;
	}

	.single--both-sidebar .single-related-posts-section-wrap.layout--list.layout--two .single-related-posts-wrap > article {
		flex: 0 1 46%;
	}
	
/** Related post layout one **/
	.single-related-posts-section .blogzee-block-title {
		font-family: var(--blogzee-widget-block-font-family);
		font-weight: var(--blogzee-widget-block-font-weight);
		font-size: calc( var(--blogzee-widget-block-font-size) * 0.98 );
		margin-bottom: 15px;
		margin-top: 0;
		letter-spacing: var(--blogzee-widget-block-font-letterspacing);
		display: block;
		color: var(--blogzee-widget-block-title-color);
		position: relative;
		line-height: calc( var(--blogzee-widget-block-font-lineheight) * 0.8 );
		text-transform: var(--blogzee-widget-block-font-texttransform);
		text-decoration: var(--blogzee-widget-block-font-textdecoration);
	}

	.block-title--three .single-related-posts-section .blogzee-block-title {
		margin-bottom: 25px;
	}

	.block-title--four .single-related-posts-section .blogzee-block-title {
		display: inline-block;
	}

	.block-title--one .single-related-posts-section .blogzee-block-title {
		padding-left: 15px;
	}

	.single-related-posts-section-wrap.layout--one .single-related-posts-wrap article .post-thumbnail {
		padding-bottom: 60%;
	}

	.single-related-posts-section-wrap.layout--one.column--three .single-related-posts-wrap article .post-thumbnail {
		padding-bottom: 70%;
	}

	.single-related-posts-section-wrap.layout--one .single-related-posts-wrap article .post-thumbnail:after,
	body #primary article.format-quote .blogzee-article-inner .post-thumbnail-wrapper:after,
	body #primary article.format-image .blogzee-article-inner .post-thumbnail-wrapper:after {
		content: '';
		background: #0000004a;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
		width: 100%;
		height: 100%;
	}

	.single-related-posts-section-wrap .single-related-posts-wrap article .post-thumbnail img {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: scale(1) translate(-50%, -50%);
		text-align: center;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.single-related-posts-section-wrap.layout--one .single-related-posts-wrap article .post-element {
		position: absolute;
		bottom: 0;
		padding: 20px;
		z-index: 2;
	}

	.single-related-posts-section-wrap.layout--list .single-related-posts-wrap article .post-element .post-title {
		color: var(--blogzee-post-title-font-color);
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	body .single-related-posts-section-wrap.layout--one .single-related-posts-wrap article .post-element .post-title a {
		color: #Fff;
	}

	.single-related-posts-section-wrap.layout--list .single-related-posts-wrap article .post-element .post-title a {
		color: inherit;
		text-decoration: none;
	}

	body.single-post .single-related-posts-section-wrap.layout--one.layout--list article .post-element .post-date i,
	body.single-post .single-related-posts-section-wrap.layout--one.layout--list article .post-element .post-meta a {
		color: #fff;
	}

	body.single-post .single-related-posts-section-wrap article .post-meta {
		margin: 0;
	}

	body.single-post .single-related-posts-section-wrap article .post-meta > span,
	body.single-post .single-related-posts-section-wrap article .post-meta .post-comments-num {
		margin: 0 12px 0 0;
	}

	body.single-post .single-related-posts-section-wrap.layout--list article .post-meta .byline a {
		text-decoration: none;
		color: inherit;
		font-size: inherit;
	}

	body.single-post .single-related-posts-section-wrap.layout--list article .post-meta .byline img {
		vertical-align: middle;
		border-radius: 50%;
		margin-right: 8px;
		width: 18px;
		height: 18px;
		-webkit-box-shadow: 1px 1px 5px 1px rgba(0,0,0,0.25);
		box-shadow: 1px 1px 5px 1px rgba(0,0,0,0.25);
	}

	body.single-post .single-related-posts-section-wrap.layout--list article .post-meta a,
	body.single-post .single-related-posts-section-wrap.layout--list article .post-meta span {
		text-decoration: none;
		color: inherit;
	}

	body.single-post .single-related-posts-section-wrap.layout--list article .post-meta i {
		padding: 0 8px 0 0;
		font-size: 12px;
	}

	body.single-post .single-related-posts-section-wrap.layout--list article .post-meta .post-button {
		display: inline-block;
		background-color: var(--blogzee-global-preset-theme-color);
		padding: 9px 20px;
		border-radius: 15px;
		color: #fff;
		margin-left: 10px;
		line-height: 1;
		box-shadow: 0 0 3px 1px rgb(224 224 224 / 25%);
		-webkit-box-shadow: 0 0 3px 1px rgb(224 224 224 / 25%);
	}

	body.single-post .single-related-posts-section-wrap.layout--list article .post-meta .post-button i {
		padding: 0;
		color: inherit;
	}

	body.single-post .single-related-posts-section-wrap.layout--list article .post-meta .post-button a {
		color: inherit;
	}

	body.single-post.single-related-posts-section-wrap.layout--list article .entry-title {
		margin-top: 20px;
		margin-bottom: 30px;
		font-size: 34px;
	}

	body.single-post .single-related-posts-section-wrap.layout--list .post-meta-wrap .post-meta {
		display: inline-block;
	}	

	body.single-post .single-related-posts-section-wrap.layout--list .post-meta-wrap .post-meta i {
		padding-left: 5px;
		padding-right: 10px;
		color: var(--blogzee-global-preset-theme-color);
	}

/* Comment Box */
	body.single-post .comments-area .comments-title {
		font-family: "Merriweather", sans-serif;
		font-weight: 600;
		font-size: 18px;
		margin-bottom: 25px;
		margin-top: 0;
		letter-spacing: 0.6px;
		padding: 0 0 10px 0;
		border-bottom: 2px solid var(--border-bottom-color);
		display: block;
		color: var(--blogzee-single-content-color);
	}

	body.single-post .comments-area .comments-title span {
		font-style: italic;
	}

	body.single-post .comments-area .comment-list {
		margin: 0;
		list-style: none;
		padding: 0;
	}

	body.single-post .comments-area .comment-list article {
		border-bottom: 1px solid var(--border-bottom-color);
	}

	body.single-post .comments-area .comment-list article:last-child {
		border-bottom: none;
	}

	body.single-post .comments-area .comment {
		display: block;
		margin-top: 20px;
		position: relative;
		padding-bottom: 20px;
	}

	body.single-post .comments-area .comment-author.vcard,
	body.single-post .comments-area .comment-metadata {
		display: flex;
		font-family: 'Montserrat';
	}

	body.single-post .comments-area .comment-metadata {
		margin: -24px 0 12px 75px;
		text-transform: uppercase;
		justify-content: space-between;
		align-items: center;
	}

	body.single-post .comments-area .comment-author.vcard a {
		font-size: 17px;
		color: var(--blogzee-single-content-color);
		text-decoration: none;
		padding-right: 10px;
		font-family: 'Montserrat';
	}

	body.single-post .comments-area .comment-author.vcard .says {
		font-size: 14px;
		font-weight: 500;
		color: var(--blogzee-single-content-color);
		margin-top: 3px;
    	margin-right: 5px;
	}

	body.single-post .comments-area .comment-metadata > a {
		font-size: 11px;
		color: var(--blogzee-single-content-color);
		text-decoration: none;
		font-family: 'Montserrat';
		font-weight: 500;
		letter-spacing: 0.8px;
	}	

	body.single-post .comments-area .comment-author.vcard img {
		width: 52px;
		height: 52px;
		border-radius: 50%;
		margin-right: 20px;
	}

	body.single-post .comments-area .comment-content {
		margin: 0 10px 20px 75px;
		color: var(--blogzee-single-content-color);
	}

	body.single-post .comments-area .reply {
		text-align: right;
	}

	body.single-post .comments-area .reply a {
		color: #fff;
		background-color: #333;
		font-size: 14px;
		font-weight: 400;
		padding: 3px 12px 4px;
		text-decoration: none;
		border: 1px solid transparent;
		font-family: var(--blogzee-post-content-font-family);
		font-style: var(--blogzee-post-content-font-style);
		vertical-align: middle;
	}

	body.single-post.blogzee-dark-mode .comments-area .reply a {
		background-color: #2b2b2b;
	}

	body.single-post .comments-area .reply a:hover {
		background-color: var(--blogzee-global-preset-theme-color);
	}

	body.single-post .comments-area .comment-reply-title {
		font-family: var(--blogzee-title-font-family);
		font-weight: 600;
		font-size: 20px;
		margin-bottom: 25px;
		margin-top: 0;
		letter-spacing: 0.6px;
		padding-bottom: 12px;
		border-bottom: 2px solid var(--border-bottom-color);
		display: block;
		color: var(--blogzee-single-content-color);
	}

	body.single-post .comments-area li > ul, body.single-post .comments-area li > ol {
		margin-bottom: 0;
		margin-left: 0.5em;
	}

	body.single-post .comments-area .form-submit input.submit {
		display: inline-block;
		background-color: #333;
		padding: 10px 15px;
		border-radius: 2px;
		line-height: 1;
		text-decoration: none;
		cursor: pointer;
		font-family: var(--blogzee-meta-font-family);
		font-size: 15px;
		color: var(--blogzee-white-text);
		border: none;
		position: relative;
		z-index: 1;
	}

	body.single-post.blogzee-dark-mode .comments-area .form-submit input.submit {
		background-color: #2b2b2b;
	}

	body.single-post .comments-area .form-submit input.submit:after {
		background-color: var(--blogzee-global-preset-theme-color);
	}

	body.single-post .comment-respond .comment-form-comment textarea {
		background-color: #f0f0f0;
		border: none;
		margin-top: 10px;
		padding: 10px 20px;
		resize: vertical;
	}

	body.single-post .comment-respond .comment-form-comment {
		margin-bottom: 15px;
	}

	body.single-post form.comment-form .comment-form-author,
	body.single-post form.comment-form .comment-form-email {
		width: 48%;
		display: inline-block;
		text-align: center;
		margin: 5px 0 15px 0;
	}

	body.single-post form.comment-form .comment-form-author {
		margin-right: 15px;
	}

	body.single-post form.comment-form .comment-form-url {
		width: 100%;
		padding-left: 5px;
		margin: 5px 0 10px 0;
	}

	body.single-post form.comment-form .comment-form-author label,
	body.single-post form.comment-form .comment-form-email label,
	body.single-post form.comment-form .comment-form-url label{
		display: block;
		text-align: left;
		padding-left: 5px;
		margin-bottom: 10px;
	}

	body.single-post form.comment-form .comment-form-author input,
	body.single-post form.comment-form .comment-form-email input {
		width: 98%;
		background-color: #f0f0f0;
		border: none;
		padding: 10px;
	}

	body.single-post form.comment-form .comment-form-url input{
		width: 98%;
		background-color: #f0f0f0;
		border: none;
		padding: 10px;
	}

	body.single-post form.comment-form input:focus,
	body.single-post form.comment-form textarea:focus {
		outline: 2px solid #c9c9c9;
	}

	body.single-post .comment-form-cookies-consent {
		padding: 5px;
		font-size: 14px;
		vertical-align: middle;
	}

	form.comment-form {
		color: var(--blogzee-single-content-color);
	}

	form.comment-form p, form.comment-form a {
		color: inherit;
	}

	body.single-post .logged-in-as a {
		text-decoration: none;
		color: var(--blogzee-global-preset-theme-color);
		text-decoration: underline;
	}

	body.single-post .form-submit {
		margin-bottom: 0;
	}

	body.single-post .wp-block-audio audio {
		background-color: #f1f3f4;
	}

	body.single-post #primary article footer .tags-links {
		margin-top: 15px;
		display: block;
		font-weight: 500;
		font-family: var(--blogzee-meta-font-family);
		text-decoration: none;
		color: var(--blogzee-post-title-font-color)
	}

	body.single-post #primary article footer .tags-links a {
		text-decoration: none;
		color: inherit;
		font-weight: 600;
		padding-left: 6px;
		font-size: 14px;
	}

	body.single-post #primary article footer .tags-links a:before {
		content: '\f02b';
		font-family: 'Font Awesome 6 Free';
		font-weight: 900;
		padding-right: 8px;
		display: inline-block;
		color: #666666;
		line-height: 1;
		font-size: 11px;
	}

	body.single-post.blogzee-dark-mode #primary article footer .tags-links a:before {
		color: #c1c1c1;
	}

	body.single-post #primary article footer .tags-links a:after {
		content: ',';
	}

	body.single-post #primary article .post-card .bmm-author-thumb-wrap {
		margin-bottom: 35px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		text-align: center;
		gap: 0;
		margin-top: 85px;
		overflow: visible;
	}

	@media (max-width: 610px) {
		body.single-post #primary article .post-card .bmm-author-thumb-wrap {
			flex-direction: column;
			text-align: center;
			align-items: center;
		}
	}

	body.single-post #primary article .post-card .bmm-author-thumb-wrap .author-elements {
		flex: 1;
	}

	body.single-post #primary article .post-card .bmm-author-thumb-wrap .post-thumb {
		width: 120px;
		height: 120px;
		margin-top: -85px;
		margin-bottom: 0;
		border-radius: 100%;
		overflow: hidden;
		padding: 8px;
	}

	body.blogzee-dark-mode.single-post #primary article .post-card .bmm-author-thumb-wrap .post-thumb {
		background-color: #222222;
	}

	body.single-post #primary article .post-card .bmm-author-thumb-wrap .post-thumb img {
		border-radius: 100px;
		width: 100%;
		height: 100%;
	}

	body.single-post #primary article .post-card .bmm-author-thumb-wrap .author-name {
		padding-top: 0;
		line-height: 12px;
		margin-bottom: 5px;
	}

	body.single-post #primary article .post-card .bmm-author-thumb-wrap .author-desc {
		font-family: var(--blogzee-post-content-font-family);
		font-size: 15px;
		color: var(--blogzee-black-dark-color);
	}

	body.single-post #primary article .post-card .bmm-author-thumb-wrap .author-name a {
		font-size: 19px;
		color: var(--blogzee-black-dark-color);
		line-height: 30px;
		text-decoration: none;
		text-transform: capitalize;
	}

	body.single-post #primary .post-navigation h2.screen-reader-text {
		display: none;
	}

	body.single-post #primary .post-navigation .nav-links {
		display: flex;
		justify-content: space-between;
	}

	body.single-post #primary .no-prev .post-navigation .nav-links {
		justify-content: flex-end;
		-webkit-justify-content: flex-end;
	}

	body.single-post #primary .post-navigation .nav-links > div {
		flex: 0 1 49%;
	}

	body.single-post #primary .post-navigation .nav-links a {
		text-decoration: none;
		display: flex;
		align-items: center;
		gap: 20px;
	}

	body.single-post #primary .post-navigation .nav-links figure.nav-thumb {
		background-size: cover;
		background-position: center;
		width: 80px;
		height: 70px;
		position: relative;
		overflow: hidden;
	}

	body.single-post #primary .post-navigation .nav-links .nav-post-elements {
		flex: 1;
	}

	body.single-post #primary .post-navigation .nav-links .nav-next .nav-post-elements {
		text-align: right;
	}

	body.single-post #primary .post-navigation .nav-links .nav-post-elements .post-date {
		margin-bottom: 3px;
	}

	body.single-post #primary .post-navigation .nav-links .nav-post-elements .post-date i {
		font-size: 10px;
	}

	body.single-post #primary .post-navigation .nav-links .nav-post-elements .post-nav-time-string {
		font-size: 12px;
		line-height: 18px;
		display: inline-block;
	}

	body.single-post #primary .post-navigation .nav-links .nav-post-elements .nav-title-wrap {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		margin-top: 5px;
	}

	body.single-post #primary .post-navigation .nav-links .nav-post-elements .nav-title {
		font-size: 1.07rem;
		line-height: inherit;
		font-style: normal;
		color: var(--blogzee-black-dark-color);
		letter-spacing: 0;
	}

	body.single-post #primary .post-navigation .nav-links .button-thumbnail {
		position: relative;
		align-self: flex-start;
	}

	body.single-post #primary .post-navigation .nav-links .nav-subtitle {
		position: absolute;
		z-index: 2;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
	}

	body.single-post #primary .post-navigation .nav-links .nav-subtitle i {
		border-radius: 2px;
		color: #fff;
		font-size: 18px;
	}

	body.single-post #primary article header.no-single-featured-image {
		height: auto;
	}

	body.single-post #blogzee-main-wrap .blogzee-container .row #primary article header.entry-header.no-single-featured-image .post-categories {
		position: relative;
		top: 0;
		left: 0px;
	}

	body.single-post #blogzee-main-wrap #primary article header.entry-header.no-single-featured-image .post-categories li {
		line-height: 15px;
		margin-bottom: 10px;
	}

	body.single-post .blogzee-inner-content-wrap .post-inner .entry-header .post-thumbnail {
		position: relative;
		padding-bottom: calc( 100% * var(--blogzee-single-post-image-ratio) );
	}

	body.single-post .entry-header .post-thumbnail img {
		height: 100%;
		width: 100%;
		object-fit: cover;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: scale(1) translate(-50%,-50%);
	}

	body.single-post #primary .post-navigation .nav-links figure.nav-thumb:before {
		width: 100%;
		height: 100%;
		content: '';
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate( -50%, -50%);
		z-index: 1;
		background-color: #0000003B;
		border-radius: 6px;
		border: transparent;
	}

	article .post-inner h1,
	article .post-inner h2,
	article .post-inner h3,
	article .post-inner h4,
	article .post-inner h5,
	article .post-inner h6 {
		margin-bottom: 10px;
		margin-top: 0;
	}

	body.single-post .entry-title {
		margin-top: 18px;
		margin-bottom: 4px;
	}

	.single.logged-in article .comment-meta .edit-link {
		position: relative;
		left: 0;
		bottom: 0;
	}

/* Single Layout Three */
	.single.single-post.single-post--layout-five #blogzee-main-wrap .blogzee-single-header {
		display: block;
	}

	.single .entry-header .post-categories li {
		display: inline-block;
		margin:4px 10px 4px 0;
	}

	.single .post-meta-wrap .post-meta .post-date {
		display: inline-block;
		margin: 10px 15px 0 0;
	}

	.single.single-post--layout-five .blogzee-single-header .entry-title {
		color: var(--blogzee-single-content-color);
	}

/* Single Layout Five */
	body.single-post.single-post--layout-five .post-categories {
		padding: 0;
		margin: 0;
	}

	body.single-post.single-post--layout-five .entry-title {
		margin-top: 12px;
	}

	.single.single-post--layout-five .post-meta-wrap {
		margin-bottom: 25px;
		padding-bottom: 20px;
		border-bottom: 1px solid #c9c9c9;
		position: relative;
	}

	body.single-post--layout-five .post-meta-wrap:after {
		content: '';
		position: absolute;
		left: 0;
		bottom: -5px;
		width: 100%;
		height: 1px;
		background: #c9c9c9;
	}

	body.single-post--layout-five .post-meta-wrap .post-meta {
		margin: 0;
	}

	body.single-post--layout-five .post-inner .post-thumbnail {
		position: relative;
		padding-bottom: calc( 100% * var(--blogzee-single-post-image-ratio) );
		margin-bottom: 25px;
	}

	body.single-post--layout-five .post-inner .post-thumbnail img {
		height: 100%;
		width: 100%;
		object-fit: cover;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: scale(1) translate(-50%, -50%);
	}


/*
=========================================
10.0 Category, Tags Author page
=========================================
*/

body.archive.category #blogzee-main-wrap .page-header,
body.archive.tag #blogzee-main-wrap .page-header,
body.archive.author #blogzee-main-wrap .page-header,
body.archive.date #blogzee-main-wrap .page-header {
	padding: 40px 20px;
	position: relative;
	z-index: 1;
	margin-bottom: 65px;
	display: flex;
	flex-direction: column;
	text-align: center;
	border-top: 1px solid var(--blogzee-border-dark-color);
	border-bottom: 1px solid var(--blogzee-border-dark-color);
}

body.search.search-results #blogzee-main-wrap .blogzee-container .page-header {
	padding: 25px 20px;
    margin-bottom: 40px;
	background: var(--blogzee-white-dark-color);
}

body.search.search-results #blogzee-main-wrap .blogzee-container .page-header .page-title {	
	font-size: 25px;
	margin: 10px 0;
	font-weight: 500;
	text-align: center;
	color: var(--blogzee-archive-text-color);
}

body.search.search-results #blogzee-main-wrap .blogzee-container .page-header .page-title span {
	padding-left: 5px;
	font-weight: 700;
}

body.archive.category #blogzee-main-wrap .page-header .archive-title,
body.archive.tag #blogzee-main-wrap .page-header .archive-title {
	display: flex;
	justify-content: center;
	-webkit-justify-content: center;
	align-items: center;
	-webkit-align-items: center;
	gap: 15px;
}

body.archive #blogzee-main-wrap .page-header .archive-title i {
	text-align: center;
	font-size: 17px;
	color: var( --blogzee-global-preset-theme-color );
}

body.archive.category #blogzee-main-wrap .page-header .page-title,
body.archive.tag #blogzee-main-wrap .page-header .page-title,
body.archive.date #blogzee-main-wrap .page-header .page-title {
	margin: 0;
}

body.archive.category #blogzee-main-wrap .page-header .archive-description p,
body.archive.tag #blogzee-main-wrap .page-header .archive-description p,
body.archive.date #blogzee-main-wrap .page-header .archive-description p {
	margin: 15px 0 0;
	text-align: center;
}

.archive.category .page-header .page-title {
	font-family: 'Montserrat', sans-serif;
	font-size: 30px;
	line-height: 33px;
	letter-spacing: 0.6px;
	font-weight: 600;
	color: var(--blogzee-archive-text-color);
}

.archive.category .page-header .archive-description {
	font-size: 16px;
	line-height: 30px;
	font-weight: 300;
	font-family: "Poppins",sans-serif;
	color: var(--blogzee-archive-text-color);
}

.archive.category .page-header .archive-description p {
	font-weight: inherit;
	letter-spacing: inherit;
}

.archive.tag .page-header .page-title {
	font-family: 'Montserrat', sans-serif;
	font-size: 30px;
	line-height: 33px;
	letter-spacing: 0.6px;
	font-weight: 600;
	color: var(--blogzee-black-dark-color);
}

.archive.tag .page-header .archive-description {
	font-size: 16px;
	line-height: 30px;
	font-weight: 300;
	font-family: "Poppins",sans-serif;
	color: var(--blogzee-black-dark-color);
}

.archive.date .page-header {
	background-color: #e9e9e9;
}

.archive.date.blogzee-dark-mode .page-header {
	background-color: #333;
}

.archive.date .page-header .page-title {
	color: var(--blogzee-archive-text-color);
	margin: 0 0 20px;
	padding: 8px 20px;
	font-size: 18px;
}

.archive.tag .page-header .archive-description p {
	font-weight: inherit;
	letter-spacing: inherit;
}

.archive.author .page-header .page-title {
	color: var(--blogzee-black-dark-color);
	margin: 10px 0 0;
}

.archive.author .page-header .archive-description {
	font-size: 16px;
	line-height: 30px;
	font-weight: 300;
	font-family: "Poppins",sans-serif;
	color: var(--blogzee-black-dark-color);
	margin-top: 15px;
	text-align: center;
}

.archive.author .page-header .archive-description p {
	font-weight: inherit;
	letter-spacing: inherit;
}


/*
=========================================
11.0 Search Popup
=========================================
*/
body.search-active {
	overflow: hidden;
}

.search-type--live-search .search-form-wrap {
	display: block;
	position: fixed;
	left: 0;
	width: 100%;
	height: 100%;
	top: 0;
	background-color: #25242421;
	z-index: 99999;
	cursor: auto;
	cursor: auto;
    backdrop-filter: blur(2px);
}

.search-type--live-search .search-form-wrap .search-model-main-wrap {
	background: #fff;
    width: 40%;
    margin-inline: auto;
    border-radius: 6px;
    box-shadow: 0 20px 25px -5px #00000017, 0 8px 10px -6px #00000017;
}

.search-type--live-search .search-form-wrap form.search-form {
	text-align: center;
	margin-top: 50vh;
	display: flex;
	position: relative;
	width: 40%;
	margin-inline: auto;
	box-shadow: 0 2px 1px #0000000f;
	border-radius: 6px;
    overflow: hidden;
}

.search-type--live-search .search-form-wrap form.search-form label {
	width: 100%;
}

.search-type--live-search .search-form-wrap form.search-form label:before {
	content: '\f002';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    z-index: 9999;
    color: var(--blogzee-black-dark-color);
    font-size: 16px;
    top: 50%;
    left: 25px;
    transform: translateY(-50%);
}

.search-type--live-search .search-form-wrap form.search-form input.search-field {
	border: none;
	width: 100%;
	padding: 18px 130px 18px 60px;
	background-color: var(--blogzee-white-dark-color);
	color: var(--blogzee-black-dark-color);
}

.blogzee-dark-mode .search-type--live-search .search-form-wrap.results-loaded form.search-form input.search-field {
	border-bottom: 1px solid #545454;
}

.search-type--live-search .search-form-wrap form.search-form input.search-field::placeholder {
	color:  var(--blogzee-black-dark-color);
}

.search-type--live-search .search-form-wrap form.search-form input.search-field:focus {
	outline: none;
}

.search-form-wrap form.search-form input.search-submit {
	background-color: var(--blogzee-global-preset-theme-color);
    border: none;
    color: #fff;
    line-height: 1;
    padding: 13px 25px;
    margin-left: 5px;
    font-family: var(--blogzee-custom-button-family);
    font-size: var(--blogzee-custom-button-size);
    font-weight: var(--blogzee-custom-button-weight);
}

.search-form-wrap form.search-form input.search-submit:hover {
	cursor: pointer;
}

/** search page search form */
	.blogzee_search_page form.search-form label {
		font-family: "Montserrat",sans-serif;
		font-weight: 600;
		font-size: 24px;
		margin-bottom: 20px;
		margin-top: 0;
		letter-spacing: 0.6px;
		padding-bottom: 5px;
		display: block;
	}

	.blogzee_search_page form.search-form  {
		position: relative;
		width: 60%;
		margin: 0 auto
	}

	.blogzee_search_page form.search-form input.search-field {
		width: 100%;
		height: 50px;
		border-radius: 50px;
		border: none;
		padding: 0 25px 0 15px;
		color: #000;
		margin-top: 15px;
		opacity: 0.815;
		border: 1px dashed #dbdbdb;
	}

	.blogzee_search_page form.search-form input.search-field:focus {
		box-shadow: none;
		outline-offset: 0;
		outline-color: var(--blogzee-global-preset-theme-color);
		min-width: 1px;
	}

	.blogzee_search_page form.search-form input.search-field:placeholder {
		color: #fff;
	}

	.no-results.not-found .blogzee_search_page form.search-form input.search-field {
		color: var(--blogzee-widget-block-title-color);
	}

	.blogzee_search_page form.search-form input.search-submit {
		position: absolute;
		top: 25px;
		right: 12px;
		color: #fff;
		background: var(--blogzee-global-preset-theme-color);
		border: none;
		font-weight: 500;
		padding: 8px 15px 9px 15px;
		border-radius: 50px;
		cursor: pointer;
		font-size: 14px;
	}

/** search result page **/
	body.search-results .no-feat-img .post-thumb-wrap {
		position: relative;
		padding-bottom: calc( 100% * 0.65 );
		background-position: center;
		background-size: cover;
		overflow: hidden;
	}

	body.search-results #blogzee-main-wrap > .blogzee-container > .row,
	body.search-no-results #blogzee-main-wrap > .blogzee-container > .row {
		padding-top: 0;
		padding-bottom: 50px;
	}

	body.search-results #blogzee-main-wrap > .blogzee-container > .row #primary article .blogzee-article-inner {
		position: relative;
		z-index: 1;
		box-sizing: border-box;
	}

	body.search-results #blogzee-main-wrap .blogzee-container .row #primary article figure {
		position: relative;
	}

	body.search-results #blogzee-main-wrap .blogzee-container .row #primary article figure.wp-block-audio {
		position: absolute;
	}

	body.search-results #blogzee-main-wrap .blogzee-container .row #primary article figure .post-categories {
		position: absolute;
		left: 10px;
		top: 15px;
		z-index: 10;
		text-align: center;

		margin: 0;
		padding: 0;
		list-style: none;
		text-decoration: none;
	}

	body.search-results article .post-meta {
		padding-bottom: 0;
	}

	body.search-results #blogzee-main-wrap .blogzee-container .row #primary article.format-video figure.wp-block-video {
		position: absolute;
		display: block;
		height: 100%;
		width: 100%;
		top: 0;
		left: 0;
		background-color: #e7e7e7e7;
		overflow: hidden;
	}

	section.no-results.not-found {
		margin: 0;
		background-color: var(--blogzee-white-bk);
		padding: 20px;
		text-align: center;
	}

	section.no-results.not-found .entry-title {
		font-size: 32px;
		margin: 10px 0;
		font-weight: 600;
		color: var(--blogzee-post-title-font-color);
	}

	section.no-results.not-found  .page-content {
		font-family: var(--blogzee-post-content-font-family);
		font-style: var(--blogzee-post-content-font-style);
		font-size: var(--blogzee-post-content-font-size);
		color: var(--blogzee-post-content-font-color);
		font-weight: var(--blogzee-post-content-font-weight);
		line-height: var(--blogzee-post-content-font-lineheight);
		letter-spacing: var(--blogzee-post-content-font-letterspacing);
		padding-bottom: 25px;
	}			   

/*
=========================================
12.0 Single Page
=========================================
*/

.wp-block-columns:last-child {
	margin-bottom: 0;
}

body.page #blogzee-main-wrap > .blogzee-container > .row,
body.error404 #blogzee-main-wrap > .blogzee-container > .row {
	padding-top: 0;
	padding-bottom: 50px;
}

.page #blogzee-main-wrap #primary article.page {
	background-color: var(--blogzee-white-bk);
	padding: 25px;
}

.error404 #blogzee-main-wrap #primary .not-found {
	background-color: rgba(0,0,0,0.15);
	padding: 20px;
	color: var(--blogzee-post-content-font-color);
	text-align: center;
}

.error404 #blogzee-main-wrap #primary .page-title {
	margin: 0;
}

body.page #blogzee-main-wrap #primary article .entry-title {
	font-family: "Montserrat",sans-serif;
	font-weight: 600;

	font-size: 34px;
	line-height: 45px;
	letter-spacing: 0.6px;
	
	position: relative;
	margin-top: 0;
	padding-bottom: 10px;
	margin-bottom: 15px;
}

.back_to_home_btn {
	padding: 5px 0;
}

.back_to_home_btn a{
	color: var(--blogzee-white-text);
	text-decoration: none;
	padding: 12px 30px;
	background: var(--blogzee-404-button-bkcolor);
	border-radius: 20px;
	border: none;
	font-family: var(--blogzee-custom-button-family);
	font-size: var(--blogzee-custom-button-size);
	font-weight: var(--blogzee-custom-button-weight);
	box-shadow: 0 0 3px 1px rgb(224 224 224 / 12%);
	-webkit-box-shadow: 0 0 3px 1px rgb(224 224 224 / 12%);
	display: inline-block;
}

.back_to_home_btn a:hover{
	color: var(--blogzee-white-text);
	background: var(--blogzee-404-button-bkcolor-hover);
}

.back_to_home_btn a i{
	padding: 0 8px;
	color: var(--blogzee-white-text);
}

.back_to_home_btn a:hover i{
	color: var(--blogzee-white-text);
}

.page-template-default #primary article .post-thumbnail {
	position: relative;
	padding-bottom: calc( 100% * var(--blogzee-single-page-image-ratio) );
	background: #efefef;
	margin-bottom: 25px;
	overflow: hidden;
}

.page-template-default #primary article .post-thumbnail img{
	height: 100%;
	width: 100%;
	object-fit: cover;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: scale(1) translate(-50%,-50%);
}

body.page-template-default article .entry-content {
	color: var(--blogzee-post-content-font-color);
	font-weight: 400;
	font-family: "Poppins", sans-serif;
	font-size: 16.5px;
	line-height: 34px;
	letter-spacing: 0.3px;
}

body.page-template-default article .entry-content p{
	margin-bottom: 20px;
	line-height: inherit;
	font-size: inherit;
	letter-spacing: inherit;
}

body.page article .entry-content p a {
	color: var(--blogzee-global-preset-theme-color);
	text-decoration: none;
}
	

/*
=========================================
13.0 Main Banner
=========================================
*/ 

.blogzee-main-banner-section {
	margin-bottom: 40px;
}

.blogzee-main-banner-section.layout--four {
	margin-bottom: 35px;
}

.blogzee-main-banner-section article.post-item .post-thumb {
	position: relative;
	padding-bottom: calc( 100% * 0.55 );
	background-position: center;
	background-size: cover;
	overflow: hidden;
	background-color: #e8e8e8;
}

.blogzee-main-banner-section article.post-item .post-thumb img {
	height: 100%;
	width: 100%;
	object-fit: cover;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: scale(1) translate(-50%,-50%);
	box-shadow: 0px 5px 15px 0px rgba(74, 77, 84, 0.33);
	--webkit-box-shadow: 0px 5px 15px 0px rgba(74, 77, 84, 0.33);
}

body .arrow-on-hover--on .swiper .swiper-arrow {
	visibility: hidden;
	opacity: 0;
}

body .arrow-on-hover--on .swiper:hover .swiper-arrow {
	visibility: visible;
	opacity: 1;
}

body .swiper-arrow {
	transition: all .3s ease;
	color: #ddd;
    background: #00000059;
    border: none;
    text-align: center;
    padding: 0;
	width: 40px;
    height: 45px;
    line-height: 44px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: block;
    z-index: 999;
	cursor: pointer;
	border-radius: 2px;
}

body .swiper .custom-button-next.swiper-arrow {
    right: 0;
}

body .swiper .custom-button-prev.swiper-arrow {
	left: 0;
}

body .swiper:hover .custom-button-next.swiper-arrow {
	right: 15px;
}

body .swiper:hover .custom-button-prev.swiper-arrow {
	left: 15px;
}

.blogzee-main-banner-section .main-banner-wrap .swiper-arrow i {
	line-height: 36px;
}

.blogzee-main-banner-section .main-banner-wrap .post-categories {
	list-style: none;
	margin: 0;
	padding: 0;
}

.blogzee-main-banner-section .swiper-slide {
	position: relative;
	cursor: grab;
}

.blogzee-main-banner-section article .post-meta {
	margin-bottom: 0;
}

.blogzee-main-banner-section .main-banner-wrap .post-categories li {
	list-style: none;
	display: inline-block;
	margin-bottom: 12px;
}

.blogzee-main-banner-section .main-banner-wrap .post-categories li + li {
	margin-left: 10px;
}

.blogzee-main-banner-section .main-banner-wrap .post-elements .post-title {
	font-family: var(--blogzee-banner-title-font-family);
	font-weight: var(--blogzee-banner-title-font-weight);
	font-size: var(--blogzee-banner-title-font-size);
	line-height: var(--blogzee-banner-title-font-lineheight);
	letter-spacing: var(--blogzee-banner-title-font-letterspacing);
	font-style: var(--blogzee-banner-title-font-style);
	text-decoration: var(--blogzee-banner-title-font-textdecoration);
	text-transform: var(--blogzee-banner-title-font-texttransform);
	margin: 0;
	padding: 0;
}

.blogzee-main-banner-section .post-elements .post-title a {
	text-decoration: none;
	color: var(--blogzee-black-dark-color);
}

.blogzee-main-banner-section .main-banner-wrap .post-elements .post-excerpt {
	font-family: var(--blogzee-banner-excerpt-font-family);
	font-style: var(--blogzee-banner-excerpt-font-style);
	font-size: var(--blogzee-banner-excerpt-font-size);
	color: #fff;
	font-weight: var(--blogzee-banner-excerpt-font-weight);
	line-height: var(--blogzee-banner-excerpt-font-lineheight);
	letter-spacing: var(--blogzee-banner-excerpt-font-letterspacing);
	text-decoration: var(--blogzee-banner-excerpt-font-textdecoration);
	text-transform: var(--blogzee-banner-excerpt-font-texttransform);
	padding: 8px 0 0;
}

.blogzee-main-banner-section .main-banner-wrap .post-elements .post-date img {
	width: 25px;
	display: inline-block;
	vertical-align: middle;
	margin-right: 10px;
	color: #fff;
}

.blogzee-central-mode-enable article {
	padding-left: 10px;
	padding-right: 10px;
}

.blogzee-main-banner-section .main-banner-wrap .byline {
	text-align: left;
	color: var(--blogzee-meta-font-color);
}

.blogzee-main-banner-section .main-banner-wrap .byline img {
	vertical-align: middle;
	border-radius: 150px;
	margin-right: 8px;
	width: 20px;
	height: 20px;
	display: inline-block;
}

.blogzee-main-banner-section .main-banner-wrap .byline a {
	text-decoration: none;
	color: inherit;
	font-size: inherit;
}

body .blogzee-main-banner-section.banner-align--left .post-elements {
	text-align: left;
}

body .blogzee-main-banner-section.banner-align--right .post-elements {
	text-align: right;
}

body .blogzee-main-banner-section.banner-align--center .post-elements {
	text-align: center;
}

/* slider */
	body .blogzee-main-banner-section figure.swiper-slide-thumb-active:before {
		content: '';
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background: var(--blogzee-global-preset-theme-color);
		opacity: .5;
		z-index: 1;
	}

	body .blogzee-main-banner-section figure.swiper-slide-thumb-active:after {
		content: '\f58d';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		font-family: 'Font Awesome 5 Free';
		font-weight: 900;
		color: #fff;
		font-size: 22px;
		z-index: 1;
	}

	.blogzee-main-banner-section .main-banner-wrap .post-item .post-elements .author-date-wrap {
		margin-top: 6px;
	}

	.blogzee-main-banner-section .main-banner-wrap .post-elements .author-date-wrap span + span {
		margin-left: 15px;
	}

	.blogzee-main-banner-section .main-banner-wrap .post-elements .post-date {
		color: var(--blogzee-banner-meta-color);
		display: inline-block;
		margin: 10px 0 0 0;
	}

	.blogzee-main-banner-section .main-banner-wrap .post-elements .post-date i {
		font-size: 12px;
	}

	/* Banner Layout Four */
	.blogzee-main-banner-section.layout--four .row {
		display: flex;
		gap: 35px
	}

	/* arrow */
	.blogzee-main-banner-section.layout--four .main-banner-slider .swiper-arrow {
		bottom: 20px;
		top: initial;
		width: 40px;
		height: 40px;
		line-height: 40px;
		background: transparent;
		text-align: center;
		color: #939393;
		transition: all 0.25s ease-in 0s;
		border-radius: 50%;
		cursor: pointer;
		outline: 1px solid #939393;
		overflow: hidden;
	}

	.blogzee-main-banner-section.layout--four .main-banner-slider .swiper-arrow:hover {
		background-color: var(--blogzee-global-preset-theme-color);
		outline: none;
		color: #fff;
	}

	.blogzee-main-banner-section.layout--four .main-banner-slider .swiper-arrow i {
		line-height: initial;
	}

	.blogzee-main-banner-section.layout--four .main-banner-slider .swiper-arrow.custom-button-prev {
		right: 110px;
		left: initial
	}

	.blogzee-main-banner-section.layout--four .main-banner-slider .swiper-arrow.custom-button-next {
		right: 60px;
	}

	/* Main Slider */
	.blogzee-main-banner-section.layout--four .main-banner-slider {
		flex: 0 1 65%;
		max-width: 65%;
	}
	
	.blogzee-main-banner-section.layout--four .main-banner-slider .post-item {
		padding-bottom: 100px;
	}

	.blogzee-main-banner-section.layout--four .main-banner-slider .post-thumb {
		border-radius: 20px;
	}

	.blogzee-main-banner-section.layout--four .main-banner-slider .post-elements {
		position: absolute;
		background: var(--blogzee-white-dark-color);
		bottom: 25px;
		z-index: 9999;
		left: 50px;
		padding: 35px;
		width: 60%;
		box-shadow: 0px 8px 25px #0000000d;
	}

	.blogzee-main-banner-section.layout--four .main-banner-slider .post-elements .post-excerpt {
		color: var(--blogzee-black-dark-color);
	}

	.blogzee-main-banner-section.layout--four .main-banner-slider .post-elements .post-categories a {
		padding: 0 12px;
	}

	/* Sidebar */
	.blogzee-main-banner-section.layout--four .main-banner-sidebar {
		flex: 0 1 35%;
	}

	/* Scrollbar */
	.blogzee-main-banner-section .scrollable-posts-wrapper::-webkit-scrollbar {
		width: 3px;
	}
	
	.blogzee-main-banner-section .scrollable-posts-wrapper::-webkit-scrollbar-thumb {
		background-color: var(--blogzee-banner-scrollbar-color);
		border-radius: 50px;
	}

	.blogzee-main-banner-section .sidebar-title {
		margin: 0 0 18px;
		color: var(--blogzee-black-dark-color);
		font-family: var(--blogzee-banner-sidebar-block-font-family);
		font-size: var(--blogzee-banner-sidebar-block-font-size);
		font-weight: var(--blogzee-banner-sidebar-block-font-weight);
		line-height: var(--blogzee-banner-sidebar-block-font-lineheight);
		letter-spacing: var(--blogzee-banner-sidebar-block-font-letterspacing);
		text-decoration: var(--blogzee-banner-sidebar-block-font-textdecoration);
		font-style: var(--blogzee-banner-sidebar-block-font-style);
	}

	.blogzee-main-banner-section.layout--four .scrollable-posts-wrapper {
		display: flex;
		flex-direction: column;
		gap: 15px;
		height: 415px;
		overflow-y: auto;
		padding-right: 5px;
	}

	.blogzee-main-banner-section.layout--four .scrollable-post {
		display: flex;
		align-items: center;
		padding: 12px;
    	background: var(--blogzee-white-dark-color);
	}

	.blogzee-main-banner-section.layout--four .scrollable-post .count-image-wrapper {
		flex: 0 1 35%;
		margin-right: 14px;
		overflow: hidden;
		height: 104px;
	}

	.blogzee-main-banner-section.layout--four .scrollable-post .post-count {
		display: none;
	}

	.blogzee-main-banner-section.layout--four .scrollable-post .count-image-wrapper .post-thumb {
		height: 100%;
		background-color: #efefef;
		overflow: hidden;
		position: relative;
	}

	.blogzee-main-banner-section.layout--four .scrollable-post .count-image-wrapper img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.blogzee-main-banner-section.layout--four .scrollable-post .title-date-wrapper {
		flex: 0 1 65%;
	}

	.blogzee-main-banner-section.layout--four .scrollable-post .title-date-wrapper .post-categories {
		padding: 0;
		margin: 0;
		list-style: none;
		display: flex;
		gap: 7px;
	}

	.blogzee-main-banner-section.layout--four .scrollable-post .title-date-wrapper .post-categories li a {
		padding: 0 12px;
		margin-bottom: 7px;
	}

	.blogzee-main-banner-section .scrollable-post .title-date-wrapper .post-title {
		margin: 0;
		font-family: var(--blogzee-banner-sidebar-title-font-family);
		font-size: var(--blogzee-banner-sidebar-title-font-size);
		font-weight: var(--blogzee-banner-sidebar-title-font-weight);
		line-height: var(--blogzee-banner-sidebar-title-font-lineheight);
		letter-spacing: var(--blogzee-banner-sidebar-title-font-letterspacing);
		text-decoration: var(--blogzee-banner-sidebar-title-font-textdecoration);
		text-transform: var(--blogzee-banner-sidebar-title-font-texttransform);
		font-style: var(--blogzee-banner-sidebar-title-font-style);
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.blogzee-main-banner-section .scrollable-post .post-title a {
		text-decoration: none;
		color: var(--blogzee-black-dark-color);
	}

	.blogzee-main-banner-section.layout--four .scrollable-post .title-date-wrapper .post-date {
		margin: 8px 0 0;
	}

	.blogzee-main-banner-section.layout--four .scrollable-post .title-date-wrapper .post-date i {
		font-size: 11px;
	}

	.blogzee-main-banner-section .scrollable-post .title-date-wrapper .post-date a {
		letter-spacing: inherit;
	}

/*
=========================================
14.0 Carousel
=========================================
*/

	/* loader */
	.blogzee-carousel-section .carousel-wrap:not(.swiper-initialized):after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		background: #0000000f;
		width: 100%;
		height: 100%;
	}

	.blogzee-carousel-section .carousel-wrap:not(.swiper-initialized) .swiper-wrapper {
		height: 360px;
	}

	.blogzee-dark-mode .blogzee-carousel-section .carousel-wrap:not(.swiper-initialized):after {
		background-color: #ffffff0f;
	}

	.blogzee-carousel-section .carousel-wrap:not(.swiper-initialized) .post-item {
		opacity: 0;
	}

	.blogzee-carousel-section {
		margin-bottom: 40px;
		overflow-y: hidden;
	}

	.blogzee-carousel-section article.post-item {
		position: relative;
		overflow: hidden;
	}

	body .blogzee-carousel-section article.post-item.hide-featured-image .post-thumb {
		display: none;
	}

	body .blogzee-carousel-section article.post-item.hide-featured-image .post-elements {
		padding: 30px;
	}

	.blogzee-carousel-section.carousel-layout--one article.post-item .post-elements {
		position: absolute;
		bottom: 20px;
		left: 50%;
		transform: translateX(-50%);
		width: 80%;
		text-align: center;
		padding: 20px 20px 15px;
		z-index: 2;
		background: #0000003d;
	}

	.blogzee-carousel-section.carousel-align--left article.post-item .post-elements {
		text-align: left;
	}

	.blogzee-carousel-section.carousel-align--right article.post-item .post-elements {
		text-align: right;
		padding: 20px 45px;
	}

	.blogzee-carousel-section article.post-item .post-thumb {
		position: relative;
		padding-bottom: calc( 100% * var(--blogzee-carousel-image-ratio) );
		background-position: center;
		background-size: cover;
		overflow: hidden;
	}

	.blogzee-carousel-section.carousel-layout--one article.post-item .post-thumb {
		padding-bottom: calc( var(--blogzee-carousel-image-ratio) * 100% * 1.6 );
	}

	.blogzee-carousel-section article.post-item .post-thumb img {
		height: 100%;
		width: 100%;
		object-fit: cover;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: scale(1) translate(-50%,-50%);
		box-shadow: 0px 5px 15px 0px rgba(74, 77, 84, 0.33);
		--webkit-box-shadow: 0px 5px 15px 0px rgba(74, 77, 84, 0.33);
	}

	.blogzee-carousel-section .carousel-wrap .post-elements .post-title {
		font-family: var(--blogzee-post-title-font-family);
		font-style: var(--blogzee-post-title-font-style);
		font-size: 24px;
		color: #fff;
		font-weight: var(--blogzee-post-title-font-weight);
		line-height: 34px;
		letter-spacing: var(--blogzee-post-title-font-letterspacing);
		margin: 0;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.blogzee-carousel-section.carousel-layout--one .carousel-wrap .post-elements .post-title {
		padding-top: 0;
	}

	.blogzee-carousel-section .post-title a {
		text-decoration: none;
		color: inherit;
		letter-spacing: inherit;
	}

	.blogzee-carousel-section .carousel-wrap .post-categories {
		list-style: none;
		margin: 0;
		padding: 0;
		margin-bottom: 10px;
	}

	.blogzee-carousel-section .carousel-wrap .slick-slide {
		position: relative;
	}

	.blogzee-carousel-section .carousel-wrap .post-categories li {
		list-style: none;
		display: inline-block;
		margin-right: 5px;
	}

	.blogzee-carousel-section .carousel-wrap .post-elements .post-excerpt {
		color: #fff;
		margin-top: 10px;
	}

	.blogzee-carousel-section .carousel-wrap .post-meta {
		margin: 10px 0 0;
	}

	.blogzee-carousel-section .carousel-wrap .byline {
		text-align: left;
		color: var(--blogzee-white-text);
		display: inline-block;
	}

	.blogzee-carousel-section .carousel-wrap .byline img {
		vertical-align: middle;
		border-radius: 50%;
		margin-right: 10px;
		width: 22px;
		height: 22px;
		display: inline-block;
	}

	.blogzee-carousel-section .carousel-wrap .byline a {
		text-decoration: none;
		color: inherit;
		font-size: inherit;
	}

	.blogzee-carousel-section .carousel-wrap .post-elements .post-meta span + span {
		margin: 0 0 0 8px;
	}

	.blogzee-carousel-section .carousel-wrap .post-elements .post-date {
		margin: 0;
		color: #fff;
	}

	.blogzee-carousel-section .post-date i {
		padding-left: 0;
	}
	

/*
=========================================
15.0 Breadcrumb
=========================================
*/

body.archive .page-header .row {
	flex-direction: column;
	gap: 15px;
}

body.archive .page-header .blogzee-breadcrumb-element.row {
	margin: 0;
}

.blogzee-breadcrumb-wrap {
	width: 100%;
	z-index: 0;
	text-align: left;
	background: var(--blogzee-global-preset-theme-color);
	color: var(--blogzee-breadcrumb-color);
	border-radius: 2px;
}

.blogzee-breadcrumb-wrap p {
	margin: 0;
}

.blogzee-breadcrumb-wrap ul {
	list-style: none;
	margin: 0;
	padding: 0;
	display: inline-block;
}

.blogzee-breadcrumb-wrap ul li {
	list-style: none;
	display: inline-block;
	padding-right: 5px;
	color: var(--blogzee-white-text);
	font-family: var(--blogzee-meta-font-family);
	font-style: var(--blogzee-site-description-style);
	word-break: break-word;
}

.blogzee-breadcrumb-wrap ul li i {
	color: var(--blogzee-breadcrumb-color);
	margin: 0 5px;
	padding-left: 7px;
	padding-right: 2px;
	margin-left: 0;
	font-size: 12px;
	font-weight: 900;
	vertical-align: middle;
}

.blogzee-breadcrumb-wrap ul li:last-child i {
	display: none;
}

.blogzee-breadcrumb-wrap ul li:after{
	color: var(--blogzee-breadcrumb-link-color);
	position: relative;
	margin: 0 5px;
	content: "\f101";
	font-family: 'Font Awesome 6 Free';
	padding-left: 7px;
	padding-right: 2px;
	margin-left: 0;
	font-size: 11px;
	font-weight: 900;
	vertical-align: middle;
	display: none;
}

.blogzee-breadcrumb-wrap ul li:last-child:after{
	content: '';
}

.blogzee-breadcrumb-wrap a {
	color: inherit;
	text-decoration: none;
	color: var(--blogzee-breadcrumb-link-color);
	text-decoration: underline;
}

.blogzee-breadcrumb-wrap a:hover {
	color: var(--blogzee-breadcrumb-link-color-hover);
}

.blogzee-breadcrumb-wrap ul li.trail-end span {
	color: var(--blogzee-breadcrumb-color);
}

body #blogzee-main-wrap > .blogzee-container > .row.blogzee-breadcrumb-element {
	padding-top: 0;
	padding-bottom: 30px;
}

.blogzee-breadcrumb-wrap ul li .item-separator img {
	width: 18px;
	height: 18px;
	margin-inline: 16px;
	vertical-align: middle;
}

.blogzee-breadcrumb-wrap ul li:last-child .item-separator img {
	display: none;
}


/*
=========================================
16.0 Canvas Section
=========================================
*/
.blogzee-model-open:after {
	content: '';
	width: 100%;
	height: 100%;
	background-color: #7a7a7a2e;
	z-index: 1;
	position: absolute;
	top: 0;
}

.blogzee-model-open:hover {
	cursor: e-resize;
}

.canvas-menu-sidebar {
	width: 370px;
	max-width: 400px;
	background: #fff;
}

.blogzee-dark-mode .canvas-menu-sidebar {
	background-color: #383838;
}

.canvas-menu-sidebar {
	position: fixed;
	top: 20px;
	visibility: hidden;
	z-index: 99999;
	transition: all 0.21s ease;
	text-align: left;
	border-radius: 20px;
	padding: 25px 10px 25px 25px;
	height: calc(100vh - 40px);
	transform: scale(.95);
    will-change: tranform;
	opacity: 0;
	overflow: hidden;
}

.logged-in .canvas-menu-sidebar {
	top: 52px;
	height: calc( 100vh - 72px );
}

body .canvas-menu-sidebar .canvas-menu-inner {
	overflow-y: auto;
    height: calc(100vh - 90px);
	padding-right: 15px;
}

.logged-in .canvas-menu-sidebar .canvas-menu-inner {
	height: calc(100vh - 122px);
}

.blogzee-canvas-position--left .canvas-menu-sidebar {
	left: 20px;
}

.blogzee-canvas-position--right .canvas-menu-sidebar {
	right: 20px;
}

body.blogzee-model-open .canvas-menu-sidebar {
	visibility: visible;
	opacity: 1;
    transform: scale(1);
	box-shadow: 0 0 4px 2px #0000000d;
}

body.blogzee-model-open.blogzee-dark-mode .canvas-menu-sidebar {
	border: 1px solid #818181;
}

body.blogzee-model-open {
	overflow-y: hidden;
}

.canvas-menu-sidebar > .widget:last-child {
	margin-bottom: 50px;
}

.canvas-menu-sidebar .canvas-menu-inner::-webkit-scrollbar {
	width: 4px;
}

.canvas-menu-sidebar .canvas-menu-inner::-webkit-scrollbar-thumb {
	background-color: var(--blogzee-global-preset-theme-color);
	border-radius: 40px;
}


/*
=========================================
17.0 Scroll To Top
=========================================
*/

.blogzee-scroll-btn.display--inline .scroll-to-top-wrapper {
	display: inline-block;
	cursor: pointer;
}

.blogzee-scroll-btn .icon-holder {
	background: #ededed;
	width: 40px;
	height: 40px;
	border-radius: 100px;
	color: #000;
	display: inline-block;
	text-align: center;
}

.blogzee-scroll-btn .icon-text {
	color: var(--blogzee-scroll-text-color);
	background: var(--blogzee-scroll-top-bk-color);
	font-size: 14px;
	border-radius: 6px;
	padding: 8px 14px;
	margin-right: 10px;
	font-family: var(--blogzee-readmore-font-family);
	font-weight: var(--blogzee-readmore-font-weight);
	cursor: pointer;
}

.blogzee-scroll-btn .icon-holder i {
	display: inline-block;
	vertical-align: middle;
	line-height: 40px;
	color: inherit;
}

/* fixed */
.blogzee-scroll-btn.display--fixed.show {
	position: fixed;
	font-size: 12px;
	bottom: 20px;
	left: 20px;
	z-index: 99;
}

.blogzee-scroll-btn.display--fixed.show .scroll-top-wrap {
	display: flex;
	align-items: center;
	-webkit-align-items: center;
}

.blogzee-scroll-btn.display--fixed.show .scroll-to-top-wrapper {
	display: flex;
	align-items: center;
	-webkit-align-items: center;
	cursor: pointer;
	width: 44px;
    height: 44px;
    border-radius: 100px;
    justify-content: center;
}

.blogzee-scroll-btn.display--fixed.align--left .icon-text {
	order: 2;	
	margin: 0 0 0 10px;
}

.blogzee-scroll-btn.display--fixed.align--center .scroll-top-wrap {
	flex-direction: column;
	justify-content: center;
}

.blogzee-scroll-btn.display--fixed.align--center.show {
	left: 50%;
    transform: translateX(-50%);
}

.blogzee-scroll-btn.display--fixed.align--center .icon-text {
	display: block;
	order: 1;
	margin-top: 10px;
	margin-right: 0;
}

.blogzee-scroll-btn.display--fixed.align--right {
	right: 20px;
	left: initial;
}

/*
=========================================
18.0 Footer
=========================================
*/

footer .bottom-inner-wrapper {
	padding: 60px 0;
}

footer .widget_blogzee_heading_widget .widget-title {
	margin: 0;
	margin-bottom: -15px;
}

footer .bottom-inner-wrapper .social-icons-wrap {
	text-align: center;
	margin-bottom: 5px
}

footer .bottom-inner-wrapper .social-icons-wrap a {
	margin-bottom: 15px;
}

footer .bottom-inner-wrapper .social-icons-wrap a {
	color: #2f2e2e;
	display: inline-block;
	margin-inline: 15px;
	line-height: 23px;
	font-size: 13px;
}

footer .site-info {
	color: var(--blogzee-white-text);
	text-align: center;
	font-family: 'Montserrat', sans-serif;
	font-size: 15px;
	color: var(--blogzee-black-dark-color);
}

footer .site-info a {
	text-decoration: none;
	color: inherit;
	color: var(--blogzee-bottom-footer-link-color);
}

footer .site-info a:hover {
	text-decoration: none;
	color: inherit;
	color: var(--blogzee-bottom-footer-link-color-hover);
}

footer .widget_block .wp-block-group__inner-container .wp-block-heading,
footer section.widget .widget-title,
footer .wp-block-heading {
	color: var(--blogzee-black-dark-color);
}

footer .wp-block-heading {
	position: relative;
}

footer ul.wp-block-latest-posts li,
footer ol.wp-block-latest-comments li,
footer ul.wp-block-archives li,
footer ul.wp-block-categories li,
footer ul.wp-block-page-list li,
footer .widget_blogzee_post_list_widget .post-list-wrap .post-item,
footer .widget_blogzee_category_collection_widget .categories-wrap .category-item,
footer .widget_blogzee_post_grid_widget .post-grid-wrap .post-item,
footer .menu .menu-item  {
	border-color: var(--blogzee-footer-widget-border-color);
}

footer .widget_blogzee_category_collection_widget .categories-wrap .category-item,
footer .widget_blogzee_post_list_widget .post-list-wrap .post-item,
footer .widget_blogzee_post_list_widget .post-list-wrap .post-title,
footer .post-date a,
footer .widget_blogzee_author_info_widget .bmm-author-thumb-wrap .author-elements .author-name,
footer .widget_blogzee_author_info_widget .bmm-author-thumb-wrap .author-elements .author-tag,
footer .author-content-wrap .author-desc,
footer .widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-title a,
footer .widget_blogzee_post_grid_widget .post-grid-wrap .post-title a,
footer .widget_blogzee_carousel_widget .post-title a,
footer .blogzee-widget-loader .load-more,
body footer .widget ul.wp-block-latest-posts a,
footer ol.wp-block-latest-comments li footer,
footer ul.wp-block-archives a,
footer ul.wp-block-categories a,
footer ul.wp-block-page-list a,
footer .widget_blogzee_post_grid_widget .post-grid-wrap .post-title,
footer .menu .menu-item a,
footer .no-comments,
footer .widget.widget_text p,
footer blockquote.wp-block-quote p,
footer blockquote cite {
	color: var(--blogzee-black-dark-color);
}

footer .widget_blogzee_post_list_widget .post-list-wrap .post-title:hover,
body footer .widget ul.wp-block-latest-posts a:hover,
footer .widget_blogzee_carousel_widget .post-title a:hover,
footer .widget_blogzee_post_grid_widget .post-grid-wrap .post-title a:hover,
footer .widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-title a:hover {
	color: var(--blogzee-global-preset-theme-color);
}

.blogzee-dark-mode .blogzee-widget-loader .load-more {
	background: #333333;
	color: #ebebeb;
	box-shadow: 0px 0px 0px 1px #d7d7d74d;
}

.blogzee-dark-mode .blogzee-widget-loader .load-more:before,
.blogzee-dark-mode .blogzee-widget-loader .load-more:after {
	background-color: #d7d7d74d;
}

footer .blogzee-widget-loader .load-more:before,
footer .blogzee-widget-loader .load-more:after {
	background-color: #ebebeb;
}

.bottom-inner-wrapper .footer-logo {
	text-align: center;
	margin-bottom: 15px;
}

body footer.site-footer .widget_blogzee_category_collection_widget .categories-wrap .category-item .category-name {
	font-size: 13px;
}


/*
=========================================
19.0 Advertisement
=========================================
*/

#blogzee-main-wrap .masonry .blogzee-advertisement-block {
	margin-bottom: 50px;
}

#primary .blogzee-inner-content-wrap .blogzee-advertisement-block img {
	display: block;
}

section.blogzee-advertisement-section-header {
	margin-top: 0;
	margin-bottom: 40px;
}

section.blogzee-advertisement img {
	display: block;
}

section.blogzee-advertisement-section-footer {
	margin-bottom: 50px;
}

.advertisement-wrap img {
	box-shadow: var(--blogzee-article-box-shadow);
	-webkit-box-shadow: var(--blogzee-article-box-shadow);
}

.blogzee-advertisement.image-option--full-width img {
	width: 100%;
}

.blogzee-advertisement.alignment--center img {
	margin: 0 auto;
}

.blogzee-advertisement.alignment--right .advertisement-wrap {
	display: flex;
	justify-content: flex-end;
}

.blogzee-advertisement.alignment--right.image-option--full-width .advertisement-wrap {
	display: block;
}


/*
=========================================
20.0 Slick Slider
=========================================
*/
/* Slider */


/*
=========================================
21.0 Hover effect and animation
=========================================
*/

/* menu hover effect */
	@media (min-width: 48.1em) {
		footer .bb-bldr-widget:not(.has-sidebar) .menu li a {
			position: relative;
		}

		body footer.site-footer .bb-bldr-widget:not(.has-sidebar) ul.menu li a:before {
			background: var(--blogzee-footer-menu-color-hover);
		}

		.main-navigation.hover-effect--one .nav-menu > li > a:before,
		footer .bb-bldr-widget:not(.has-sidebar) .menu.hover-effect--one li a:before {
			content: "";
			width: 100%;
			height: 2px;
			position: absolute;
			left: 0;
			bottom: -5px;
			background: var(--blogzee-menu-color-hover);
			transition: 0.5s transform ease;
			transform: scale3d(0,1,1);
			transform-origin: 50% 50%;
		}

		.main-navigation.hover-effect--one .nav-menu li:hover > a:before,
		footer .bb-bldr-widget:not(.has-sidebar) .menu.hover-effect--one li a:hover:before {
			transform: scale3d(1,1,1);
			transform-origin: 50% 0;
		}

		/* hover two */
		.main-navigation.hover-effect--two .nav-menu > li > a:before,
		footer .bb-bldr-widget:not(.has-sidebar) .menu.hover-effect--two li a:before {
			content: "";
			width: 100%;
			height: 2px;
			position: absolute;
			left: 0;
			bottom: -5px;
			background: var(--blogzee-menu-color-hover);
			transition: 0.5s transform ease;
			transform: scale3d(0,1,1);
			transform-origin: 100% 50%;
		}

		.main-navigation.hover-effect--two .nav-menu > li:hover > a:before,
		footer .bb-bldr-widget:not(.has-sidebar) .menu.hover-effect--two li a:hover:before {
		  	transform-origin: 0 50%;
		  	transform: scale3d(1,1,1);
		}

		/* hover three */
		.main-navigation.hover-effect--three .nav-menu > li > a:before,
		footer .bb-bldr-widget:not(.has-sidebar) .menu.hover-effect--three li a:before {
			content: "";
			width: 100%;
			height: 2px;
			position: absolute;
			left: 0;
			bottom: -5px;
			background: var(--blogzee-menu-color-hover);
			transition: 0.5s transform ease;
			transform: scale3d(0,1,1);
			transform-origin: 0 50%;
		}

		.main-navigation.hover-effect--three .nav-menu > li:hover > a:before,
		footer .bb-bldr-widget:not(.has-sidebar) .menu.hover-effect--three li a:hover:before {
		  	transform: scale3d(1,1,1);
		  	transform-origin: 100% 50%;
		}

		/* hover four */
		.main-navigation.hover-effect--four .nav-menu > li > a:before,
		footer .bb-bldr-widget:not(.has-sidebar) .menu.hover-effect--four li a:before {
			background-color: var(--blogzee-menu-color-hover);
			border-radius: 2px;
			bottom: -2px;
			content: "";
			height: 2px;
			left: 0;
			opacity: 1;
			position: absolute;
			transform: scaleX(0);
			transform-origin: 100% 50%;
			transition: transform .3s cubic-bezier(.2,1,.3,1);
			width: 16px;
		}

		.main-navigation.hover-effect--four .nav-menu > li:hover > a:before,
		.main-navigation.hover-effect--four .nav-menu > li.current-menu-item > a:before,
		.main-navigation.hover-effect--four .nav-menu > li.current_page_item > a:before,
		footer .bb-bldr-widget:not(.has-sidebar) .menu.hover-effect--four li a:hover:before {
			transform: scaleX(1);
    		transform-origin: 0 50%;
		}
	}

/** button hover effect */
	article .post-categories li a,
	.tags-wrap .tags-item,
	.pagination ul .page-numbers,
	.widget .post-categories .cat-item,
	body .swiper-arrow,
	.wp-block-search__button,
	.social-platforms-widget.global-color-icon i,
	.blogzee-scroll-btn .icon-holder,
	.back_to_home_btn a,
	body.single-post #primary .post-navigation .nav-links .nav-subtitle,
	body.single-post form.comment-form .submit,
	.site-header .search-wrap  .search-form-close,
	body.single-post .post-categories .cat-item a,
	.ajax-load-more-wrap span {
		-webkit-transition: all 250ms ease;
		-o-transition: all 250ms ease;
		transition: all 250ms ease;
	}

	.pagination ul .page-numbers:hover,
	.social-platforms-widget.global-color-icon i:hover,
	.back_to_home_btn a:hover {
		-webkit-transform: translateY(-2px);
		-ms-transform: translateY(-2px);
		transform: translateY(-2px);
	}

/* Title effect Seven */
	.title-hover--seven .entry-title a,
	.title-hover--seven .post-title a,
	.title-hover--seven ul.wp-block-latest-posts a,
	.title-hover--seven ul.wp-block-page-list a,
	.title-hover--seven .widget.widget_categories ul li a,
	.title-hover--seven ul.wp-block-archives a {
		-webkit-transition: background-size .25s cubic-bezier(.32,.74,.57,1);
		transition: background-size .25s cubic-bezier(.32,.74,.57,1);
		background-repeat: no-repeat;
		background-position: 0 100%;
		background-size: 100% 0%;
		background-image: var( --blogzee-global-preset-gradient-theme-color );
	}

	.title-hover--seven .entry-title a:hover,
	.title-hover--seven .post-title a:hover,
	.title-hover--seven ul.wp-block-latest-posts a:hover,
	.title-hover--seven ul.wp-block-latest-posts a:hover,
	.title-hover--seven ul.wp-block-page-list a:hover,
	.title-hover--seven .widget.widget_categories ul li a:hover,
	.title-hover--seven ul.wp-block-archives a:hover {
		background-size: 100% 16%;
	}
	
/* Title effect Eight */
	.title-hover--eight .entry-title a,
	.title-hover--eight .post-title a,
	.title-hover--eight ul.wp-block-latest-posts a,
	.title-hover--eight ul.wp-block-latest-posts a,
	.title-hover--eight ul.wp-block-page-list a,
	.title-hover--eight .widget.widget_categories ul li a,
	.title-hover--eight ul.wp-block-archives a {
		transition: all .3s ease-in-out;
	}

	.title-hover--eight .entry-title a:hover,
	.title-hover--eight .post-title a:hover,
	.title-hover--eight ul.wp-block-latest-posts a:hover,
	.title-hover--eight ul.wp-block-latest-posts a:hover,
	.title-hover--eight ul.wp-block-page-list a:hover,
	.title-hover--eight .widget.widget_categories ul li a:hover,
	.title-hover--eight ul.wp-block-archives a:hover,
	body.title-hover--eight .single-related-posts-section-wrap.layout--two .post-title a:hover,
	.title-hover--eight .search-wrap.search-type--live-search .article-item .post-element .post-title a:hover {
		color: var(--blogzee-global-preset-theme-color);
	}

/* Image hover effect One */
	.single-related-posts-section-wrap .single-related-posts-wrap .post-thumbnail {
		position: relative;
		overflow: hidden;
	}

	body.image-hover--three article figure.post-thumbnail-wrapper .post-thumnail-inner-wrapper .thumbnail-gallery-slider img {
		transform: initial;
	}

/* Image hover effect Three */
	.image-hover--three .blogzee-carousel-section article.post-item .post-thumb img,
	.image-hover--three .blogzee-main-banner-section article.post-item .post-thumb img,
	.image-hover--three .widget_blogzee_post_grid_widget .post-grid-wrap .post-thumb-image img,
	.image-hover--three .widget_blogzee_post_list_widget .post-list-wrap .post-thumb-image img,
	.image-hover--three .nekit-widget-section article.post-item .post-thumb-wrap img {
		-webkit-transform: scale(1) translate(-50%,-50%);
		transform: scale(1) translate(-50%,-50%);
		-webkit-transition: .3s ease-in-out;
		transition: .3s ease-in-out;
	}

	.image-hover--three article figure.post-thumbnail-wrapper .post-thumnail-inner-wrapper img,
	.image-hover--three .widget_blogzee_carousel_widget .post-thumb-wrap img,
	.image-hover--three .widget_blogzee_category_collection_widget .categories-wrap .category-item img,
	.image-hover--three .widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-thumb img,
	.image-hover--three .nekit-widget-section.nekit-banner-wrap article.post-item .post-thumb-wrap img,
	.image-hover--three .nekit-widget-section.nekit-banner-wrap article.post-item .post-thumb img,
	.image-hover--three .main-banner-sidebar .post-thumb img {
		-webkit-transform: scale(1);
		transform: scale(1);
		-webkit-transition: all 0.4s ease-in-out 0s;
		 -moz-transition: all 0.4s ease-in-out 0s;
		 transition: all 0.4s ease-in-out 0s;
	}

	.image-hover--three .blogzee-carousel-section article.post-item .post-thumb:hover img,
	.image-hover--three .blogzee-main-banner-section article.post-item .post-thumb:hover img,
	.image-hover--three .widget_blogzee_post_grid_widget .post-grid-wrap .post-thumb-image:hover img,
	.image-hover--three .widget_blogzee_post_list_widget .post-list-wrap .post-thumb-image:hover img,
	body.image-hover--three .blogzee-you-may-have-missed-section article figure.post-thumbnail-wrapper:hover .post-thumnail-inner-wrapper img,
	.image-hover--three .nekit-widget-section article.post-item:hover .post-thumb-wrap img {
		-webkit-transform: scale(1.02) translate(-50%,-50%);
		transform: scale(1.02) translate(-50%,-50%);
	}

	body.image-hover--three .blogzee-you-may-have-missed-section article figure.post-thumbnail-wrapper:hover .post-thumnail-inner-wrapper img,
	.image-hover--three article figure.post-thumbnail-wrapper .post-thumnail-inner-wrapper:hover img,
	.image-hover--three .widget_blogzee_carousel_widget .post-thumb-wrap:hover img,
	.image-hover--three .widget_blogzee_category_collection_widget .categories-wrap .category-item:hover img,
	.image-hover--three .widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-thumb:hover img,
	.image-hover--three .nekit-widget-section.nekit-banner-wrap article.post-item:hover .post-thumb-wrap img,
	.image-hover--three .nekit-widget-section.nekit-banner-wrap article.post-item:hover .post-thumb img,
	.image-hover--three .main-banner-sidebar .post-thumb:hover img {
		-webkit-transform: scale(1.03);
		transform: scale(1.03);
	}

	.image-hover--three .single-related-posts-section-wrap .single-related-posts-wrap .post-thumbnail img {
		-webkit-transform: scale(1.1) translate(-50%, -50%);
		transform: scale(1.1) translate(-50%, -50%);
		-webkit-transition: .4s ease-in-out;
		transition: .4s ease-in-out;
		height: 100%;
		width: 100%;
		object-fit: cover;
		position: absolute;
		top: 50%;
		left: 50%;
	}

	.image-hover--three .single-related-posts-section-wrap .single-related-posts-wrap .post-thumbnail:hover img {
		-webkit-transform: scale(1) translate(-50%, -51%);
		transform: scale(1) translate(-50%, -51%);
	}

/* Image hover effect five */
	body.image-hover--five article figure.post-thumbnail-wrapper .post-thumnail-inner-wrapper .thumbnail-gallery-slider img {
		transform: initial;
	}

	.image-hover--five article figure.post-thumbnail-wrapper .post-thumnail-inner-wrapper img,
	.image-hover--five .blogzee-carousel-section article.post-item .post-thumb img,
	.image-hover--five .blogzee-main-banner-section article.post-item .post-thumb img,
	.image-hover--five .widget_blogzee_post_grid_widget .post-grid-wrap .post-thumb-image img,
	.image-hover--five .widget_blogzee_post_list_widget .post-list-wrap .post-thumb-image img,
	.image-hover--five .single-related-posts-section-wrap .single-related-posts-wrap .post-thumbnail img,
	.image-hover--five .widget_blogzee_carousel_widget .post-thumb-wrap img,
	.image-hover--five .widget_blogzee_category_collection_widget .categories-wrap .category-item img,
	.image-hover--five .widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-thumb img,
	.image-hover--five .blogzee-you-may-have-missed-section article figure.post-thumbnail-wrapper img,
	.image-hover--five .nekit-widget-section article.post-item .post-thumb-wrap img,
	.image-hover--five .nekit-widget-section article.post-item .post-thumb img,
	.image-hover--five .main-banner-sidebar .post-thumb img {
		-webkit-transition: -webkit-transform 1s cubic-bezier(.25,.04,0,.93);
		-moz-transition: -moz-transform 1s cubic-bezier(.25, .04, 0, .93);
		transition: transform 1s cubic-bezier(.25,.04,0,.93);
		-webkit-transform: translate3d(0, 0, 0) scale(1.06);
		-moz-transform: translate3d(0, 0, 0) scale(1.06);
		transform: translate3d(0, 0, 0) scale(1.06);
		top: initial;
		left: 0;
	}

	.image-hover--five article figure.post-thumbnail-wrapper .post-thumnail-inner-wrapper:hover img,
	.image-hover--five .blogzee-carousel-section article.post-item .post-thumb:hover img,
	.image-hover--five .blogzee-main-banner-section article.post-item .post-thumb:hover img,
	.image-hover--five .widget_blogzee_post_grid_widget .post-grid-wrap .post-thumb-image:hover img,
	.image-hover--five .widget_blogzee_post_list_widget .post-list-wrap .post-thumb-image:hover img,
	.image-hover--five .single-related-posts-section-wrap .single-related-posts-wrap .post-thumbnail:hover img,
	.image-hover--five .widget_blogzee_carousel_widget .post-thumb-wrap:hover img,
	.image-hover--five .widget_blogzee_category_collection_widget .categories-wrap .category-item:hover img,
	.image-hover--five .widget_blogzee_posts_grid_two_column_widget .posts-wrap .post-thumb:hover img,
	.image-hover--five .blogzee-you-may-have-missed-section article figure.post-thumbnail-wrapper:hover img,
	.image-hover--five .nekit-widget-section article.post-item:hover .post-thumb-wrap img,
	.image-hover--five .nekit-widget-section article.post-item:hover .post-thumb img,
	.image-hover--five .main-banner-sidebar .post-thumb:hover img {
		-webkit-transform: translate3d(3%, 0, 0) scale(1.06);
		-moz-transform: translate3d(3%, 0, 0) scale(1.06);
		transform: translate3d(3%, 0, 0) scale(1.06);
	}

/** Sidebar stickey **/
	body.blogzee-stickey-sidebar--enabled .row #primary,
	body.blogzee-stickey-sidebar--enabled #blogzee-main-wrap aside#secondary-aside,
	body.blogzee-stickey-sidebar--enabled #blogzee-main-wrap aside#secondary {
		position: -webkit-sticky;
		position: sticky;
		top: 0;
		align-self: flex-start;
	}

	body.admin-bar.blogzee-stickey-sidebar--enabled .row #primary,
	body.admin-bar.blogzee-stickey-sidebar--enabled #blogzee-main-wrap aside#secondary-aside,
	body.admin-bar.blogzee-stickey-sidebar--enabled #blogzee-main-wrap aside#secondary {
		top: 35px;
	}

	body.single-post #primary .blogzee-inner-content-wrap article .entry-content a,
	body.single-post #primary article footer .tags-links a {
		text-decoration: underline;
	}

	.logged-in .site-main article .blogzee-article-inner ~ .edit-link {
		position: absolute;
		font-size: 13px;
	}

	.logged-in .site-main article .blogzee-article-inner ~ .edit-link a {
		color: #046bd2;
	}

	.logged-in article .edit-link a:hover {
		text-decoration: underline;
	}

	.single.logged-in article .edit-link {
		position: relative;
	}

	.single-post #blogzee-main-wrap .blogzee-container .row #primary .post-inner,
	.single-post #blogzee-main-wrap .blogzee-container .row #primary .comments-area,
	.single-post #primary article .post-card .bmm-author-thumb-wrap,
	.single-post #blogzee-main-wrap .blogzee-container .row #primary nav.navigation,
	.single-post #blogzee-main-wrap .blogzee-container .row #primary .single-related-posts-section-wrap {
		background: #fff;
	}

	.single.logged-in #blogzee-main-wrap .blogzee-container .row #primary div.post-inner {
		padding-bottom: 10px;
	}

	body.single.logged-in article .edit-link a {
		font-size: 11px;
		color: #046bd2;
		font-family: 'Montserrat';
		font-weight: 500;
	}

	.logged-in article .edit-link a:before {
		font-family: "Font Awesome 5 Free";
		content: "\f044";
		font-weight: 900;
		display: inline-block;
		padding-right: 5px;
	}

/*
=========================================
23.0 Live Search
=========================================
*/

.search-form-wrap.results-loaded form.search-form {
	margin-top: 10vh;
	transition: .3s ease;
}   

.search-wrap.search-type--live-search .search-results-wrap {
	width: 40%;
	margin: 0 auto;
	padding: 30px 25px 20px;
	background: var(--blogzee-white-dark-color);
	text-align: left;
	border-radius: 0 0 6px 6px;
    margin-top: -5px;
}

.blogzee-dark-mode .search-wrap.search-type--live-search .search-results-wrap {
	background-color: #333;
}

.search-wrap.search-type--live-search .search-results-wrap .search-posts-wrap {
	max-height: 290px;
	overflow-y: scroll;
	padding-right: 8px;
	margin-bottom: 10px;
} 

.search-wrap.search-type--live-search .article-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	overflow: hidden;
}

.search-wrap.search-type--live-search .article-item + .article-item {
    margin-top: 15px;
}

.search-wrap.search-type--live-search .article-item figure.post-thumb-wrap {
	flex: 0 1 20%;
	max-height: 110px;
	border-radius: 6px;
	overflow: hidden;
	margin-right: 15px;
}

.search-wrap.search-type--live-search .article-item figure.post-thumb-wrap img {
	display: block;
	height: 100%;
	width: 100%;
	object-fit: cover;
}

.search-wrap.search-type--live-search .article-item .post-element {
	flex: 1 1 73%;
	text-align: left;
}

.search-wrap.search-type--live-search .article-item .post-element .post-title {
	font-family: var(--blogzee-widget-title-font-family);
	font-style: var(--blogzee-widget-title-font-style);
	font-size: 0.97rem;
	color: var(--blogzee-widget-title-font-weight);
	font-weight: var(--blogzee-widget-title-font-weight);
	line-height: calc( var(--blogzee-widget-title-font-lineheight) * 0.84 );
	letter-spacing: var(--blogzee-widget-title-font-letterspacing);
	text-transform: var(--blogzee-widget-title-font-texttransform);
	text-decoration: var(--blogzee-widget-title-font-textdecoration);
	color: var(--blogzee-black-dark-color);
	padding: 0;
	margin: 0 0 3px;
	overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.search-wrap.search-type--live-search .article-item .post-element .post-title a{
	color: inherit;
	text-decoration: none;
}

.search-wrap.search-type--live-search .no-posts-found-title {
	text-align: center;
	color: var(--blogzee-black-dark-color);
}

.search-wrap.search-type--live-search .search-results-wrap .search-posts-wrap::-webkit-scrollbar {
	width: 2px;
}

.search-wrap.search-type--live-search .search-results-wrap .search-posts-wrap::-webkit-scrollbar-thumb {
	background-color: var(--blogzee-black-dark-color);
	border-radius: 40px;
}

.search-wrap.search-type--live-search .search-results-wrap .view-all-search-button {
	color: var(--blogzee-search-viewall-color);
	text-decoration: none;
	padding: 12px 30px;
	background: var(--blogzee-search-viewall-bkcolor);
	border: none;
	font-family: var(--blogzee-custom-button-family);
	font-size: var(--blogzee-custom-button-size);
	font-weight: var(--blogzee-custom-button-weight);
	box-shadow: 0 0 3px 1px rgb(224 224 224 / 12%);
	-webkit-box-shadow: 0 0 3px 1px rgb(224 224 224 / 12%);
	display: inline-block;
	margin-top: 10px;
	position: relative;
	z-index: 1;
	overflow: hidden;
	transition: all 1s;
	width: 100%;
    text-align: center;
    outline: 1px solid #e5e5e5;
    border-radius: 3px;
}

.search-wrap.search-type--live-search .search-results-wrap .view-all-search-button:hover {
	color: var(--blogzee-search-viewall-color-hover);
}

body:not(.blogzee-dark-mode) .search-wrap.search-type--live-search .search-results-wrap .view-all-search-button:hover:after {
	background: var(--blogzee-search-viewall-bkcolor-hover);
	height: 900%;
}

.blogzee-dark-mode .search-wrap.search-type--live-search .search-results-wrap .view-all-search-button {
	background-color: #fff;
	color: #333;
}

/*  Image Animation  */
	/* Animation One */
		.blogzee-category-collection-section.hover-effect--one .category-wrap .category-thumb a:after {
			content: '';
			display: block;
			position: absolute;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			border-radius: inherit;
			border: 2px solid #ffffffb3;
			transform: scale(1);
			opacity: 0;
			transition: .6s ease;
			z-index: 1;
		}

		.blogzee-category-collection-section.hover-effect--one .category-wrap:hover .category-thumb a:after {
			transform: scaleX(0.92) scaleY(0.88);
			opacity: 1;
		}

/*
=========================================
25.0 Category Collection
=========================================
*/

.blogzee-category-collection-section {
	margin-bottom: 40px;
}

.blogzee-category-collection-section .category-collection-wrap {
	display: grid;
	gap: 24px;
}

/*  Desktop Column  */
	.blogzee-category-collection-section.column--four .category-collection-wrap {
		grid-template-columns: repeat(4, 1fr);
	}

	.blogzee-category-collection-section.column--three .category-collection-wrap {
		grid-template-columns: repeat(3, 1fr);
	}

	.blogzee-category-collection-section.column--two .category-collection-wrap {
		grid-template-columns: repeat(2, 1fr);
	}

	.blogzee-category-collection-section.column--one .category-collection-wrap {
		grid-template-columns: 100%;
	}

/*  Tab Column  */
	@media (max-width: 768px) {
		.blogzee-category-collection-section.tab-column--four .category-collection-wrap {
			grid-template-columns: repeat(4, 1fr);
		}

		.blogzee-category-collection-section.tab-column--three .category-collection-wrap {
			grid-template-columns: repeat(3, 1fr);
		}

		.blogzee-category-collection-section.tab-column--two .category-collection-wrap {
			grid-template-columns: repeat(2, 1fr);
		}

		.blogzee-category-collection-section.tab-column--one .category-collection-wrap {
			grid-template-columns: 100%;
		}
	}

/*  Mobile Column  */
	@media (max-width: 610px) {
		.blogzee-category-collection-section.mobile-column--four .category-collection-wrap {
			grid-template-columns: repeat(4, 1fr);
		}

		.blogzee-category-collection-section.mobile-column--three .category-collection-wrap {
			grid-template-columns: repeat(3, 1fr);
		}

		.blogzee-category-collection-section.mobile-column--two .category-collection-wrap {
			grid-template-columns: repeat(2, 1fr);
		}

		.blogzee-category-collection-section.mobile-column--one .category-collection-wrap {
			grid-template-columns: 100%;
		}
	}

.blogzee-category-collection-section .category-wrap {
	position: relative
}

.blogzee-category-collection-section .category-wrap:before {
	content: '';
	display: block;
	padding-bottom: 250px;
}

.blogzee-category-collection-section .category-wrap .category-thumb a {
	height: 100%;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background-color: #e7e7e7e7;
	overflow: hidden;
}

.blogzee-category-collection-section.layout--one .category-wrap .category-item {
	position: absolute;
	bottom: 30px;
    left: 30px;
	z-index: 1;
}

.blogzee-category-collection-section .category-wrap .cat-meta .category-label {
	max-width: 170px;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.blogzee-category-collection-section .category-wrap .category-name a {
	text-decoration: none;
}

.blogzee-category-collection-section.layout--one .category-wrap .category-name a {
	display: flex;
	flex-direction: column;
	gap: 4px;
	padding: 10px 16px;
	border-radius: 6px;
	background-color: #00000057;
}

.blogzee-category-collection-section .category-wrap .category-name .category-label {
	color: #fff;
}

.blogzee-category-collection-section .category-wrap .category-label,
.blogzee-category-collection-section .category-wrap .category-count {
	font-family: var(--blogzee-category-collection-font-family);
	font-style: var(--blogzee-category-collection-font-style);
	font-size: var(--blogzee-category-collection-font-size);
	line-height: var(--blogzee-category-collection-font-lineheight);
	letter-spacing: var(--blogzee-category-collection-font-letterspacing);
	font-weight: var(--blogzee-category-collection-font-weight);
	text-decoration: var(--blogzee-category-collection-font-textdecoration);
	text-transform: var(--blogzee-category-collection-font-texttransform);
}

.blogzee-category-collection-section .category-wrap .category-count {
	color: #fff;
	font-size: calc( var(--blogzee-category-collection-font-size) * 0.78 );
}

.blogzee-category-collection-section .category-wrap img {
	display: block;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* Layout Two */

	.blogzee-category-collection-section.column--three .category-wrap:before {
		padding-bottom: 280px;
	}

	.blogzee-category-collection-section.column--two .category-wrap:before {
		padding-bottom: 320px;
	}


/* 
=====================
Header
=====================
*/

.top-date-time {
	background-color: var(--blogzee-date-time-bk-color);
}

.top-date-time .top-date-time-inner {
	font-family: var(--blogzee-date-time-family);
	font-size: var(--blogzee-date-time-size);
	font-weight: var(--blogzee-date-time-weight);
	line-height: var(--blogzee-date-time-lineheight);
	letter-spacing: var(--blogzee-date-time-letterspacing);
	text-transform: var(--blogzee-date-time-texttransform);
	text-decoration: var(--blogzee-date-time-textdecoration);
	font-style: var(--blogzee-date-time-style);
}

.top-date-time .time {
	color: var(--blogzee-time-color);
}

.top-date-time .date {
	color: var(--blogzee-date-color);
}

header .social-icons-wrap a + a,
footer .social-icons-wrap a + a {
	margin-left: 20px;
}

header .social-icons-wrap a {
	color: var(--blogzee-header-social-color);
}

footer .social-icons-wrap a {
	color: var(--blogzee-footer-social-color);
}

.social-icons-wrap.blogzee-show-hover-animation a {
	transition: all .25s cubic-bezier(.02,.01,.45,1);
	display: inline-block;
}

header .social-icons-wrap.blogzee-show-hover-animation a:hover,
footer .social-icons-wrap.blogzee-show-hover-animation a:hover {
	transform: translateY(-4px);
}

header .social-icons-wrap a:hover {
	color: var(--blogzee-header-social-color-hover);
}

footer .social-icons-wrap a:hover {
	color: var(--blogzee-footer-social-color-hover);
}

/*
=========================================
26.0 Table Of Content
=========================================
*/

.toc-wrapper {
	margin-bottom: 20px;
}

.blogzee-table-of-content .table-of-content-title-wrap {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.blogzee-table-of-content .table-of-content-title-wrap .toc-title {
	padding: 0;
	margin: 0;
	font-size: 24px;
	color: var(--blogzee-single-content-color);
}

.blogzee-table-of-content .table-of-content-title-wrap .toc-title:before {
	content: '\f022';
	font-family: 'Font Awesome 5 Free';
	font-style: normal;
	font-size: 22px;
	margin-right: 15px;
	vertical-align: top;
	font-weight: 400;
}

.blogzee-table-of-content .table-of-content-title-wrap .toc-icon {
	cursor: pointer;
	font-size: 14px;
	color: var(--blogzee-single-content-color);
}

.blogzee-table-of-content.display--fixed .table-of-content-title-wrap .toc-icon {
	color: #fff;
}

.blogzee-table-of-content .table-of-content-list-wrap {
	margin: 12px 10px 0;
	display: block;
}

.blogzee-table-of-content.table-view--tree .toc-list-item-wrap {
	padding-left: 0;
	margin-left: 25px;
}

.blogzee-table-of-content.table-view--tree .toc-list-item-wrap:first-child,
.blogzee-table-of-content.table-view--flat .toc-list-item-wrap {
	padding: 0;
	margin: 0;
}

.blogzee-table-of-content .toc-list-item-wrap {
	list-style: none;
}

.blogzee-table-of-content .toc-list-item,
.blogzee-table-of-content .toc-list-item li {
	position: relative;
}

.blogzee-table-of-content .toc-list-item + .toc-list-item,
.blogzee-table-of-content .toc-list-item-wrap {
	margin-top: 4px;
}

.blogzee-table-of-content .toc-list-item-wrap .toc-heading-title a {
	color: var(--blogzee-single-content-color);
	font-family: var(--blogzee-post-content-font-family);
	font-style: var(--blogzee-post-content-font-style);
	font-size: calc(var(--blogzee-post-content-font-size) * 0.95);
	font-weight: var(--blogzee-post-content-font-weight);
	line-height: var(--blogzee-post-content-font-lineheight);
	letter-spacing: var(--blogzee-post-content-font-letterspacing);
	text-transform: var(--blogzee-post-content-font-texttransform);
	text-decoration: var(--blogzee-post-content-font-textdecoration);
	position: relative;
}

.blogzee-table-of-content.list-type--symbol .toc-list-item-wrap .toc-heading-title {
	padding-left: 20px;
	display: inline-block;
}

.blogzee-table-of-content.list-type--symbol .toc-list-item-wrap .toc-heading-title a:before {
	content: '';
	position: absolute;
	top: 50%;
	left: -20px;
	transform: translateY(-50%);
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background-color: var(--blogzee-single-content-color);
}

.blogzee-table-of-content.list-type--symbol.display--fixed .toc-list-item-wrap .toc-heading-title a:before {
	background-color: #fff;
}

.blogzee-table-of-content.list-type--number .toc-list-item-wrap .toc-heading-title .numbering-prefix {
	margin-right: 5px;
}

.blogzee-table-of-content.display--inline .toc-fixed-icon {
	display: none;
}

.blogzee-table-of-content.display--inline .toc-wrapper {
	border: 1px solid #efefef;
	padding: 15px 20px;
	background: #00000005;
}

/* Icon */
	.blogzee-table-of-content.list-type--icon .toc-heading-icon {
		font-size: 10px;
		margin-right: 8px;
	}

/* Fixed */
	.blogzee-table-of-content.display--fixed .toc-fixed-icon {
		color: #fff;
		cursor: pointer;
		position: fixed;
		top: 210px;
		left: 0;
		z-index: 5;
		background: #000000;
		padding: 7px 20px 7px 12px;
		border-radius: 0 30px 30px 0;
	}

	.blogzee-table-of-content.display--fixed .toc-fixed-icon .toc-fixed-title {
		font-size: 15px;
	}

	.blogzee-table-of-content.display--fixed .toc-fixed-icon .toc-fixed-title.hide-on-mobile {
		display: none;
	}

	.blogzee-table-of-content.display--fixed .toc-fixed-icon .toc-fixed-title.hide-on-mobile ~ i {
		margin: 0;
	}

	.blogzee-table-of-content.display--fixed .toc-fixed-icon i {
		font-size: 14px;
		margin-left: 10px;
	}

	.blogzee-table-of-content.display--fixed .toc-wrapper {
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		background: #161616;
		margin: 0;
		padding: 30px;
		width: 330px;
		z-index: 99999;
		visibility: hidden;
		transition: .4s ease-in-out;
		transition-property: transform, visibility;
		content-visibility: auto;
		transform: translate3d(-100%, 0, 0);
		overflow-y: auto;
	}

	.logged-in .blogzee-table-of-content.display--fixed .toc-wrapper {
		margin-top: 25px;
	}

	.blogzee-table-of-content.display--fixed .toc-wrapper.active {
		transform: translate3d(0, 0, 0);
		visibility: visible;
	}

	.blogzee-table-of-content.display--fixed .table-of-content-list-wrap {
		margin-top: 15px;
		margin-bottom: 20px;
	}

	.blogzee-table-of-content.display--fixed .table-of-content-title-wrap .toc-title,
	.blogzee-table-of-content.display--fixed .toc-list-item-wrap .toc-heading-title a,
	.blogzee-table-of-content.display--fixed.list-type--icon .toc-heading-icon {
		color: #fff;
	}

	.blogzee-table-of-content.display--fixed.table-view--tree .toc-list-item-wrap {
		padding: 0;
		margin: 0;
	}

	.blogzee-table-of-content.display--fixed.table-view--tree .toc-list-item-wrap li {
		margin-top: 10px;
	}


/* 
==========================
You May Have Missed
==========================
*/

.blogzee-you-may-have-missed-section .row {
	display: block !important;
}

.blogzee-you-may-have-missed-section .section-title {
	position: relative;
	margin-bottom: 18px;
	padding-bottom: 5px;
	color: var(--blogzee-youmaymissed-block-title-color);
	font-family: var(--blogzee-youmaymissed-block-title-font-family);
	font-size: var(--blogzee-youmaymissed-block-title-font-size);
	line-height: var(--blogzee-youmaymissed-block-title-font-lineheight);
	letter-spacing: var(--blogzee-youmaymissed-block-title-font-letterspacing);
	font-weight: var(--blogzee-youmaymissed-block-title-font-weight);
	text-decoration: var(--blogzee-youmaymissed-block-title-font-textdecoration);
	text-transform: var(--blogzee-youmaymissed-block-title-font-texttransform);
	font-style: var(--blogzee-youmaymissed-block-title-font-style);
}

.block-title--four .blogzee-you-may-have-missed-section .section-title {
	display: inline-block;
}

.block-title--three .blogzee-you-may-have-missed-section .section-title {
	padding-bottom: 12px;
}

.blogzee-you-may-have-missed-section .you-may-have-missed-wrap {
	display: grid;
	grid-template-columns: 100%;
	gap: 20px;
}

.blogzee-you-may-have-missed-section .you-may-have-missed-wrap .post-thumbnail-wrapper:before {
	content: '';
	display: block;
	padding-bottom: calc(100%* var(--blogzee-youmaymissed-image-ratio));
}

.blogzee-you-may-have-missed-section.section--grid .you-may-have-missed-wrap .post-thumbnail-wrapper:after {
	content: '';
	width: 100%;
	height: 100%;
	background: #00000033;
	position: absolute;
	z-index: 1;
	top: 0;
	left: 0;
}

.blogzee-you-may-have-missed-section .post-categories {
	margin: 0 0 5px 0;
	padding: 0;
	list-style: none;
}

.blogzee-you-may-have-missed-section .post-categories li {
	display: inline-block;
	margin: 0 8px 6px 0;
}

.blogzee-you-may-have-missed-section .post-item .entry-title {
	font-family: var(--blogzee-youmaymissed-title-font-family);
	font-size: var(--blogzee-youmaymissed-title-font-size);
	line-height: var(--blogzee-youmaymissed-title-font-lineheight);
	letter-spacing: var(--blogzee-youmaymissed-title-font-letterspacing);
	font-weight: var(--blogzee-youmaymissed-title-font-weight);
	text-decoration: var(--blogzee-youmaymissed-title-font-textdecoration);
	text-transform: var(--blogzee-youmaymissed-title-font-texttransform);
	font-style: var(--blogzee-youmaymissed-title-font-style);
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.blogzee-you-may-have-missed-section .post-item .entry-title {
	margin: 0;
}

.blogzee-you-may-have-missed-section .post-item .post-categories a {
	font-family: var(--blogzee-youmaymissed-category-font-family);
	font-size: var(--blogzee-youmaymissed-category-font-size);
	line-height: var(--blogzee-youmaymissed-category-font-lineheight);
	letter-spacing: var(--blogzee-youmaymissed-category-font-letterspacing);
	font-weight: var(--blogzee-youmaymissed-category-font-weight);
	text-decoration: var(--blogzee-youmaymissed-category-font-textdecoration);
	text-transform: var(--blogzee-youmaymissed-category-font-texttransform);
	font-style: var(--blogzee-youmaymissed-category-font-style);
}

.blogzee-you-may-have-missed-section .post-item .post-meta {
	margin: 10px 0 0;
}

.blogzee-you-may-have-missed-section .post-item .post-date {
	font-family: var(--blogzee-youmaymissed-date-font-family);
	font-size: var(--blogzee-youmaymissed-date-font-size);
	line-height: var(--blogzee-youmaymissed-date-font-lineheight);
	letter-spacing: var(--blogzee-youmaymissed-date-font-letterspacing);
	font-weight: var(--blogzee-youmaymissed-date-font-weight);
	text-decoration: var(--blogzee-youmaymissed-date-font-textdecoration);
	text-transform: var(--blogzee-youmaymissed-date-font-texttransform);
	font-style: var(--blogzee-youmaymissed-date-font-style);
	margin: 0;
}

.blogzee-you-may-have-missed-section .post-item .author {
	font-family: var(--blogzee-youmaymissed-author-font-family);
	font-size: var(--blogzee-youmaymissed-author-font-size);
	line-height: var(--blogzee-youmaymissed-author-font-lineheight);
	letter-spacing: var(--blogzee-youmaymissed-author-font-letterspacing);
	font-weight: var(--blogzee-youmaymissed-author-font-weight);
	text-decoration: var(--blogzee-youmaymissed-author-font-textdecoration);
	text-transform: var(--blogzee-youmaymissed-author-font-texttransform);
	font-style: var(--blogzee-youmaymissed-author-font-style);
}

/* Column */
	.blogzee-you-may-have-missed-section.no-of-columns--four .you-may-have-missed-wrap {
		grid-template-columns: repeat(4, 1fr);
	}

	.blogzee-you-may-have-missed-section.no-of-columns--three .you-may-have-missed-wrap {
		grid-template-columns: repeat(3, 1fr);
	}

	.blogzee-you-may-have-missed-section.no-of-columns--two .you-may-have-missed-wrap {
		grid-template-columns: repeat(2, 1fr);
	}

.blogzee-you-may-have-missed-section article.post-item .post-thumnail-inner-wrapper a {
	height: 100%;
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background-color: #e7e7e7e7;
	overflow: hidden;
}

.blogzee-you-may-have-missed-section article.post-item .post-thumnail-inner-wrapper a img {
	display: block;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* Layout Grid */
	.blogzee-you-may-have-missed-section.section--grid .post-thumbnail-wrapper {
		position: relative;
	}

	.blogzee-you-may-have-missed-section.section--grid .post-thumbnail-wrapper .inner-content {
		position: absolute;
		bottom: 0;
		left: 0;
		z-index: 2;
		width: 100%;
		padding: 20px 20px 18px;
	}

	.blogzee-you-may-have-missed-section.you-may-have-missed-align--center .content-wrap {
		text-align: center;
	}

	.blogzee-you-may-have-missed-section.you-may-have-missed-align--right .content-wrap {
		text-align: right;
	}

	.blogzee-you-may-have-missed-section.section--grid .post-thumbnail-wrapper .entry-title a {
		color: #fff;
	}

	.blogzee-you-may-have-missed-section.section--grid .post-thumbnail-wrapper .author a,
	.blogzee-you-may-have-missed-section.section--grid .post-thumbnail-wrapper .post-date a {
		color: #fff;
	}

	.blogzee-you-may-have-missed-section article .post-meta .byline {
		margin-left: 0;
	}

	.blogzee-you-may-have-missed-section .post-item .post-meta .post-date {
		display: inline-block;
		margin-top: 5px;
	}

	.blogzee-you-may-have-missed-section:not(.section--list) .post-item .post-meta .post-date {
		color: #fff;
	}

	.blogzee-you-may-have-missed-section .post-thumbnail-wrapper .post-meta .byline img {
		width: 22px;
		height: 22px;
	}
	
	body.blogzee-dark-mode .blogzee-you-may-have-missed-section .section-title {
		border-color:#515151;
	}

/* Ticker News */
	.blogzee-ticker-news {
		margin-bottom: 30px;
	}

	.blogzee-ticker-news .row {
		display: flex;
		overflow: hidden;
	}

	.blogzee-ticker-news .ticker-title-wrapper {
		background: var(--blogzee-global-preset-theme-color);
		padding: 5px 16px;
		display: flex;
		align-items: center;
		gap: 12px;
	}

	.blogzee-ticker-news .ticker-title-wrapper .ticker-icon {
		display: block;
		position: relative;
		border-radius: 50%;
		height: 7px;
		width: 7px;
		background-color: #fff;
	}
	
	.blogzee-ticker-news .ticker-title-wrapper .ticker-icon:before,
	.blogzee-ticker-news .ticker-title-wrapper .ticker-icon:after {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 50%;
		border: 1px solid #fff;
		animation: divider-heading-pulse 2s ease-in-out infinite;
	}
	
	@keyframes divider-heading-pulse {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		100% {
			transform: scale(3);
			opacity: 0;
		}
	}

	.blogzee-ticker-news .ticker-title-wrapper .ticker-title {
		margin: 0;
		color: #fff;
		font-size: 16px;
	}

	.blogzee-ticker-news .ticker-news-wrap {
		overflow: hidden;
		max-height: 52px;
		width: 100%;
		background-color: var(--blogzee-white-bk);
	}

	.blogzee-ticker-news .ticker-news-wrap .ticker-item-wrap {
		padding: 0;
		margin: 0;
		list-style: none;
	}

	.blogzee-ticker-news .ticker-news-wrap .ticker-item-wrap .js-marquee {
		display: flex;
		align-items: center;
		padding: 7px 0;
		gap: 20px;
	}

	.blogzee-ticker-news .ticker-news-wrap ul.ticker-item-wrap li {
		display: flex;
		align-items: center;
	}

	.blogzee-ticker-news .ticker-news-wrap .ticker-item .post-thumb {
		max-width: 50px;
		margin-right: 12px;
		overflow: hidden;
		height: 100%;
	}

	.blogzee-ticker-news .ticker-news-wrap .ticker-item .post-thumb.no-feat-image {
		display: none;
	}

	.blogzee-ticker-news .ticker-news-wrap .ticker-item .post-thumb img {
		height: 100%;
		object-fit: cover;
	}

	.blogzee-ticker-news .ticker-news-wrap .ticker-item .title-wrap {
		max-width: 370px;
	}

	.blogzee-ticker-news .ticker-news-wrap .ticker-item .title-wrap .post-title {
		margin: 0;
		font-size: 12px;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}
	
	.blogzee-ticker-news .post-title a {
		text-decoration: none;
		color: var(--blogzee-black-dark-color);
		display: block;
	}

	.blogzee-ticker-news .ticker-news-wrap .ticker-item .title-wrap .post-date {
		margin: 3px 0 0;
	}

	.blogzee-ticker-news .ticker-news-wrap .ticker-item .title-wrap .post-date i {
		font-size: 11px;
	}

	.blogzee-ticker-news .controller-wrapper .controller-icon {
		height: 100%;
		cursor: pointer;
		background-color: #000;
		color: #fff;
		border: none;
		border-radius: 0;
		padding: 0 15px;
	}


/*
=============================
Elementor Compatibility
=============================
*/

.nekit-widget-section article .post-meta {
	margin: 0;
}

article .nekit-item-box-wrap .post-meta i {
	padding: 0;
}

.nekit .post-title a {
	letter-spacing: inherit;
}

/* dark mode */
.nekit_dark_mode::before {
    background-color: #000 !important;
    color: #fff !important;
}

body.nekit_dark_mode .site-header,
body.nekit_dark_mode .site-footer,
body.nekit_dark_mode .site-header .bb-bldr-row,
body.nekit_dark_mode .site-footer .bb-bldr-row,
body.nekit_dark_mode.page #blogzee-main-wrap #primary article.page,
body.nekit_dark_mode section:not(.main-banner-section) {
	background-color: #222 !important;
}

body.nekit_dark_mode .widget_block.widget_search .wp-block-search__label {
	color: #fff;
}

.nekit_dark_mode .nekit-banner-wrap button {
	background-color: transparent !important;
}

/* preview edit shortcut */
body.customize-partial-edit-shortcuts-shown header.site-header {
	position: relative;
	border: 2px solid #3582c4;
}

body.customize-partial-edit-shortcuts-shown .customize-partial-edit-shortcut.customize-partial-edit-shortcut-header_builder_section_tab {
	height: 100%;
}

body.customize-partial-edit-shortcuts-shown .customize-partial-edit-shortcut.customize-partial-edit-shortcut-header_builder_section_tab button {
	left: 0;
	top: initial;
    bottom: -40px;
}

body.customize-partial-edit-shortcuts-shown .customize-partial-edit-shortcut.customize-partial-edit-shortcut-header_builder_section_tab button:after,
body.customize-partial-edit-shortcuts-shown footer .customize-partial-edit-shortcut.customize-partial-edit-shortcut-footer_section_tab button:after {
    font-size: 15px;
    width: 102px;
    position: absolute;
    font-family: 'Outfit';
    color: #ffffff;
    text-shadow: none;
    font-weight: 500;
    background: #3582c4;
    padding: 5px;
    top: 0;
    left: 35px;
    line-height: 20px;
}

body.customize-partial-edit-shortcuts-shown .customize-partial-edit-shortcut.customize-partial-edit-shortcut-header_builder_section_tab button:after {
	content: 'Edit Header';
}

body.customize-partial-edit-shortcuts-shown footer.site-footer {
	border: 2px solid #3582c4;
}

body.customize-partial-edit-shortcuts-shown footer .customize-partial-edit-shortcut.customize-partial-edit-shortcut-footer_section_tab button:after {
	content: 'Edit Footer';
}

body.customize-partial-edit-shortcuts-shown footer .customize-partial-edit-shortcut.customize-partial-edit-shortcut-footer_section_tab button {
	left: 0;
	top: -40px;
}

/*
=========================================
27.0 Infinite Scroll
=========================================
*/

/* Infinite Scroll Loading Indicator */
.blogzee-infinite-scroll-loading {
	text-align: center;
	padding: 40px 20px;
	margin: 30px 0;
}

.blogzee-infinite-scroll-loading .loading-spinner {
	display: inline-flex;
	align-items: center;
	gap: 15px;
	font-family: var(--blogzee-main-font-family);
	color: var(--blogzee-post-content-font-color);
}

.blogzee-infinite-scroll-loading .spinner {
	width: 24px;
	height: 24px;
	border: 3px solid var(--blogzee-border-dark-color);
	border-top: 3px solid var(--blogzee-global-preset-theme-color);
	border-radius: 50%;
	animation: blogzee-spin 1s linear infinite;
}

@keyframes blogzee-spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.blogzee-infinite-scroll-loading .loading-text {
	font-size: 16px;
	font-weight: 500;
	letter-spacing: 0.3px;
}

/* End of Content Message */
.blogzee-infinite-scroll-end {
	text-align: center;
	padding: 40px 20px;
	margin: 30px 0;
	border-top: 2px solid var(--blogzee-border-dark-color);
}

.blogzee-infinite-scroll-end .end-message {
	font-family: var(--blogzee-main-font-family);
	font-size: 16px;
	font-weight: 500;
	color: var(--blogzee-post-content-font-color);
	margin: 0;
	opacity: 0.8;
}

/* Error Message */
.blogzee-infinite-scroll-error {
	text-align: center;
	padding: 30px 20px;
	margin: 30px 0;
	background-color: #fff3cd;
	border: 1px solid #ffeaa7;
	border-radius: 8px;
}

.blogzee-infinite-scroll-error .error-message {
	font-family: var(--blogzee-main-font-family);
	font-size: 15px;
	color: #856404;
	margin: 0 0 15px 0;
}

.blogzee-infinite-scroll-error .retry-button {
	background-color: var(--blogzee-global-preset-theme-color);
	color: #fff;
	border: none;
	padding: 10px 20px;
	border-radius: 5px;
	font-family: var(--blogzee-main-font-family);
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.blogzee-infinite-scroll-error .retry-button:hover {
	background-color: var(--blogzee-global-preset-color-3);
	transform: translateY(-2px);
}

/* Load More Button (Fallback) */
.blogzee-load-more-wrapper {
	text-align: center;
	padding: 40px 20px;
	margin: 30px 0;
}

.blogzee-load-more-btn {
	background-color: var(--blogzee-global-preset-theme-color);
	color: #fff;
	border: none;
	padding: 15px 30px;
	border-radius: 8px;
	font-family: var(--blogzee-main-font-family);
	font-size: 16px;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

.blogzee-load-more-btn:hover {
	background-color: var(--blogzee-global-preset-color-3);
	transform: translateY(-3px);
	box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.blogzee-load-more-btn:active {
	transform: translateY(-1px);
}

/* New Posts Animation */
.blogzee-inner-content-wrap article {
	transition: opacity 0.6s ease-in-out;
}

/* Dark Mode Support */
.blogzee-dark-mode .blogzee-infinite-scroll-loading .loading-text,
.blogzee-dark-mode .blogzee-infinite-scroll-end .end-message {
	color: var(--blogzee-post-content-font-color);
}

.blogzee-dark-mode .blogzee-infinite-scroll-loading .spinner {
	border-color: var(--blogzee-border-dark-color);
	border-top-color: var(--blogzee-global-preset-theme-color);
}

.blogzee-dark-mode .blogzee-infinite-scroll-end {
	border-top-color: var(--blogzee-border-dark-color);
}

.blogzee-dark-mode .blogzee-infinite-scroll-error {
	background-color: #3a3a3a;
	border-color: #555;
}

.blogzee-dark-mode .blogzee-infinite-scroll-error .error-message {
	color: #ffd700;
}

/* Mobile Responsive */
@media (max-width: 768px) {
	.blogzee-infinite-scroll-loading,
	.blogzee-infinite-scroll-end,
	.blogzee-infinite-scroll-error,
	.blogzee-load-more-wrapper {
		padding: 30px 15px;
		margin: 20px 0;
	}

	.blogzee-infinite-scroll-loading .loading-text {
		font-size: 14px;
	}

	.blogzee-infinite-scroll-end .end-message {
		font-size: 14px;
	}

	.blogzee-load-more-btn {
		padding: 12px 25px;
		font-size: 14px;
	}
}

/* Hide default pagination when infinite scroll is active */
.infinite-scroll .pagination {
	display: none !important;
}