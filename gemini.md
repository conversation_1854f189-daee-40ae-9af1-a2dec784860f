# Gemini SEO Guidelines for WordPress Theme Development

## Introduction

This document provides a comprehensive set of Search Engine Optimization (SEO) guidelines that must be followed during all development for this WordPress theme project. The goal is to ensure that any code developed is optimized for performance, search engine crawlability, and user experience, laying a strong foundation for SEO success. This document should be referenced in all future development tasks to ensure consistent SEO implementation across the theme.

---

### 1. Performance & Loading Speed

**Core Principle:** A fast website is crucial for both user experience and search rankings.

- **Target Page Load Times:** All pages should aim for a load time of **under 3 seconds** on a standard internet connection.
- **Image Optimization:**
    - **Requirement:** All images must be optimized. Use the **WebP format** for a superior compression-to-quality ratio. Implement **lazy loading** for all below-the-fold images and iframes. Serve images that are appropriately sized for their containers to avoid unnecessary browser resizing.
    - **Code Example (Lazy Loading):** WordPress 5.5+ handles this natively. Ensure `loading="lazy"` is present on `<img>` tags.
      ```html
      <img src="image.webp" alt="A descriptive alt text for the image" loading="lazy" width="800" height="600">
      ```
    - **Testing:** Use Google PageSpeed Insights to identify unoptimized images. Manually inspect image elements in browser developer tools to check for lazy loading attributes and correct dimensions.
- **Minimize CSS/JS & Eliminate Render-Blocking Resources:**
    - **Requirement:** All CSS and JavaScript assets must be minified. Combine files where feasible. Critical CSS should be inlined, and non-critical CSS/JS should be deferred or loaded asynchronously to prevent render-blocking.
    - **Code Example (Deferring a script in `functions.php`):**
      ```php
      add_filter( 'script_loader_tag', function ( $tag, $handle ) {
          if ( 'my-non-critical-script' !== $handle ) {
              return $tag;
          }
          return str_replace( ' src', ' defer="defer" src', $tag );
      }, 10, 2 );
      ```
    - **Testing:** Use Google PageSpeed Insights and WebPageTest to identify render-blocking resources.
- **Implement Caching Strategies:**
    - **Requirement:** The theme must work seamlessly with popular caching plugins (e.g., W3 Total Cache, WP Super Cache). This includes page caching, browser caching, and object caching (with Redis or Memcached).
    - **Testing:** Check HTTP response headers (e.g., `Cache-Control`, `Expires`) in browser developer tools to ensure content is being cached correctly.
- **Google Lighthouse Score:**
    - **Requirement:** All key pages must maintain a Google Lighthouse **performance score of 90+**.
    - **Testing:** Regularly run Lighthouse audits via Chrome DevTools or Google PageSpeed Insights.

---

### 2. Site Structure & Navigation

**Core Principle:** A logical site structure helps search engines and users find content easily.

- **URL Structures:**
    - **Requirement:** URLs must be clean, logical, and human-readable. In WordPress, set the permalink structure to **"Post name"** (`/%postname%/`).
    - **Testing:** Verify the structure in `Settings > Permalinks`.
- **Internal Linking Hierarchy:**
    - **Requirement:** Implement a clear internal linking strategy. Key pages should be accessible from the main navigation. Contextual links should be used within content to guide users and search engines to related pages.
    - **Testing:** Use an SEO tool like Screaming Frog to crawl the site and analyze the internal link graph.
- **Semantic HTML5:**
    - **Requirement:** Use semantic HTML5 elements correctly in all theme templates to define the structure of the page.
    - **Code Example:**
      ```html
      <header> <!-- Site header --> </header>
      <nav> <!-- Main navigation --> </nav>
      <main>
        <article> <!-- Main content for a post or page --> </article>
      </main>
      <aside> <!-- Sidebar --> </aside>
      <footer> <!-- Site footer --> </footer>
      ```
    - **Testing:** Inspect the DOM in browser developer tools. Use the W3C HTML validator to check for proper element usage.
- **Breadcrumb Navigation:**
    - **Requirement:** Implement breadcrumbs for all pages except the homepage to improve orientation.
    - **Implementation:** This can be done via an SEO plugin or a custom function. The theme must support it.
    - **Code Example (Using Yoast SEO breadcrumbs in `page.php`):**
      ```php
      if ( function_exists('yoast_breadcrumb') ) {
        yoast_breadcrumb( '<p id="breadcrumbs">','</p>' );
      }
      ```
    - **Testing:** Ensure breadcrumbs are present, accurate, and link correctly.
- **XML Sitemaps:**
    - **Requirement:** An accurate XML sitemap must be generated.
    - **Implementation:** This is handled by WordPress core (since 5.5) or an SEO plugin. The theme must not interfere with its generation.
    - **Testing:** Check for a valid sitemap at `/sitemap.xml` or `/sitemap_index.xml`.

---

### 3. Content Optimization

**Core Principle:** Content must be high-quality, well-structured, and optimized for relevant keywords.

- **Heading Hierarchy (H1, H2, etc.):**
    - **Requirement:** Each page must have **exactly one `<h1>` tag** for the main title. Subheadings must follow a logical hierarchy (`<h2>`, `<h3>`, etc.) without skipping levels.
    - **Testing:** Use the "Document Outline" feature in browser developer extensions to check the heading structure.
- **Title Tags & Meta Descriptions:**
    - **Requirement:** Title tags should be unique and **50-60 characters** long. Meta descriptions should be unique and **150-160 characters** long.
    - **Implementation:** The theme must declare `title-tag` support and let SEO plugins manage these.
    - **Testing:** "View Source" in the browser to inspect the `<title>` and `<meta name="description">` tags.
- **Image Alt Text:**
    - **Requirement:** All meaningful images must have descriptive `alt` text. Decorative images should have an empty alt attribute (`alt=""`).
    - **Testing:** Inspect all `<img>` tags in browser developer tools.
- **Structured Data / Schema Markup:**
    - **Requirement:** Implement structured data (e.g., `Article`, `Product`, `BreadcrumbList`) using JSON-LD to help search engines understand the content.
    - **Implementation:** Use an SEO plugin or inject JSON-LD scripts via the `wp_head` hook.
    - **Testing:** Use the Google Rich Results Test to validate the implementation.
- **Content Readability:**
    - **Requirement:** Ensure text is easy to read with a base font size of at least **16px**, sufficient color contrast (WCAG AA standard), and adequate line spacing.
    - **Testing:** Use Lighthouse accessibility audits.

---

### 4. Technical SEO

**Core Principle:** A technically sound site is the foundation for all other SEO efforts.

- **Mobile-First Responsive Design:**
    - **Requirement:** The theme must be fully responsive and provide an excellent experience on all devices, from mobile phones to desktops.
    - **Testing:** Use the Google Mobile-Friendly Test and the device emulator in Chrome DevTools.
- **Canonical URLs:**
    - **Requirement:** Use `rel="canonical"` tags on all pages to specify the preferred URL, preventing duplicate content issues.
    - **Implementation:** WordPress and SEO plugins handle this. The theme must not override it.
    - **Testing:** Inspect the `<head>` of the page source for the canonical link.
- **Open Graph & Twitter Card Meta Tags:**
    - **Requirement:** Include Open Graph and Twitter Card tags to ensure rich snippets when content is shared on social media.
    - **Implementation:** This is best handled by an SEO plugin.
    - **Testing:** Use the Facebook Sharing Debugger and Twitter Card Validator.
- **robots.txt:**
    - **Requirement:** A `robots.txt` file must be present to instruct search engine crawlers.
    - **Implementation:** WordPress creates a default virtual `robots.txt`. A physical file can be added to the root for custom directives.
    - **Testing:** Navigate to `domain.com/robots.txt`.
- **HTTPS and Security Headers:**
    - **Requirement:** The entire site must be served over **HTTPS**. Security headers (like HSTS) should be implemented.
    - **Implementation:** HTTPS is a server-level configuration.
    - **Testing:** Check for the lock icon in the browser address bar. Use a tool like securityheaders.com to audit.

---

### 5. WordPress-Specific Requirements

**Core Principle:** Leverage WordPress best practices to build a robust and compatible theme.

- **WordPress Coding Standards:**
    - **Requirement:** All PHP, HTML, CSS, and JavaScript must adhere to the official WordPress Coding Standards.
    - **Testing:** Use `PHP_CodeSniffer` with the WordPress coding standards ruleset to validate code.
- **SEO Plugin Compatibility:**
    - **Requirement:** The theme must be fully compatible with major SEO plugins like **Yoast SEO** and **Rank Math**. It should not duplicate functionality provided by these plugins (e.g., sitemaps, meta tags).
    - **Testing:** Install and configure these plugins. Verify that their features work as expected without conflicts.
- **WordPress Hooks for SEO:**
    - **Requirement:** Use WordPress hooks for any SEO-related customizations. Avoid hardcoding values. The theme must properly use `wp_head()` and `wp_footer()`.
    - **Implementation:** Ensure `add_theme_support('title-tag');` is present in `functions.php` to let WordPress and plugins manage the title tag.
    - **Testing:** Verify that plugins can successfully add scripts and tags to the `<head>`.
- **Database Query Optimization:**
    - **Requirement:** All database queries must be efficient. Avoid slow queries, especially on high-traffic pages. Use caching for the results of complex queries.
    - **Testing:** Use the **Query Monitor** plugin to analyze the type, source, and performance of all database queries on a given page.
