# Testing Directory

This directory contains all testing scripts, verification tools, and test-related utilities for the WordPress project.

## Directory Structure

```
testing/
├── README.md                    # This file
├── utilities/                   # Testing utilities and tools
│   ├── verify-file-organization.php  # File organization verification
│   └── README.md               # Utilities documentation
├── vote-system/                 # Vote system testing scripts
│   ├── test-vote-fix.php       # Comprehensive vote bug test script
│   └── verify-fix.php          # Simple verification script
└── [other-features]/           # Future testing directories for other features
```

## Vote System Testing

### Available Scripts

1. **test-vote-fix.php**
   - Comprehensive testing interface for the vote counting bug fix
   - Provides web-based testing with detailed output
   - Simulates the exact bug scenario reported by users
   - **Usage**: Access via web browser (admin privileges required)

2. **verify-fix.php**
   - Simple command-line verification script
   - Quick validation of the bug fix
   - Minimal output for automated testing
   - **Usage**: Run via PHP CLI or web browser

### Running Tests

#### Web-based Testing
1. Ensure you have admin privileges in WordPress
2. Navigate to the testing script in your browser:
   - `your-site.com/testing/vote-system/test-vote-fix.php`
   - `your-site.com/testing/vote-system/verify-fix.php`

#### Command-line Testing
```bash
# From project root
cd testing/vote-system/
php verify-fix.php
```

### Test Scenarios

The vote system tests cover:
- ✅ Initial state verification (0 likes, 0 dislikes)
- ✅ First user vote recording
- ✅ Second user vote recording (bug scenario)
- ✅ Database integrity checks
- ✅ Vote counting accuracy

### Expected Results

**Before Fix**: Like=2, Dislike=1 (incorrect)
**After Fix**: Like=2, Dislike=0 (correct)

## Adding New Tests

When adding new testing scripts:

1. Create a dedicated subdirectory for the feature being tested
2. Include both comprehensive and simple verification scripts
3. Update this README with the new testing information
4. Ensure all scripts include proper error handling and security checks

## Security Notes

- All testing scripts require admin privileges
- Scripts include WordPress nonce verification
- Database operations are properly sanitized
- Test data is isolated and can be safely cleared

## File Organization

This testing directory follows the project's file organization standards:
- Production code stays in main directories (`themes/`, `plugins/`)
- Testing code is isolated in the `testing/` directory
- Documentation is in the `docs/` directory
- Each feature has its own testing subdirectory
