# Testing Utilities

This directory contains utility scripts and tools that support testing across the entire project.

## Available Utilities

### verify-file-organization.php
**Purpose**: Verifies that the project follows established file organization guidelines.

**Features**:
- ✅ Checks production directories for non-production files
- ✅ Verifies required directory structure exists
- ✅ Counts testing and documentation files
- ✅ Reports compliance status

**Usage**:
```bash
# From project root
php testing/utilities/verify-file-organization.php
```

**Exit Codes**:
- `0`: All files properly organized
- `1`: File organization issues found

## Adding New Utilities

When creating new testing utilities:

1. **Naming**: Use descriptive names like `verify-[what].php` or `check-[what].php`
2. **Documentation**: Include purpose and usage in file header
3. **Error Handling**: Provide clear error messages and appropriate exit codes
4. **Standards**: Follow the project's file organization guidelines
5. **README**: Update this file with new utility information

## Guidelines

- Keep utilities generic and reusable across features
- Include proper error handling and validation
- Use consistent output formatting
- Document all command-line options and exit codes
- Test utilities before committing
