<?php
/**
 * File Organization Verification Script
 * 
 * This script checks that the project follows the established file organization guidelines.
 * It verifies that production directories contain only production code and that testing/documentation
 * files are properly organized in their designated directories.
 */

// Configuration
$project_root = dirname(dirname(__DIR__));
$production_dirs = ['themes', 'plugins'];  // Exclude wordpress core from checks
$non_production_extensions = ['.test.php', '.debug.php', '.tmp', '.bak'];
$non_production_patterns = [
    '/test-.*\.php$/',
    '/verify-.*\.php$/',
    '/debug-.*\.php$/',
    '/.*-test\.php$/',
    '/.*-debug\.php$/',
    '/.*\.test\..*$/',
    '/.*\.debug\..*$/',
    '/.*BUG.*REPORT.*\.md$/i',
    '/.*TEST.*\.md$/i'
];

// Directories to exclude from checks (WordPress core, etc.)
$excluded_paths = [
    'wordpress/',  // WordPress core files may contain debug/test keywords
];

echo "🔍 File Organization Verification\n";
echo "================================\n\n";

$issues_found = 0;

// Check production directories for non-production files
foreach ($production_dirs as $dir) {
    $dir_path = $project_root . '/' . $dir;
    if (!is_dir($dir_path)) {
        continue;
    }
    
    echo "Checking production directory: $dir/\n";
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir_path, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($files as $file) {
        $filename = $file->getFilename();
        $relative_path = str_replace($project_root . '/', '', $file->getPathname());
        
        // Check for non-production file extensions
        foreach ($non_production_extensions as $ext) {
            if (str_ends_with($filename, $ext)) {
                echo "  ❌ Non-production file found: $relative_path\n";
                $issues_found++;
            }
        }
        
        // Check for non-production file patterns
        foreach ($non_production_patterns as $pattern) {
            if (preg_match($pattern, $filename)) {
                echo "  ❌ Non-production file found: $relative_path\n";
                $issues_found++;
            }
        }
    }
}

// Check that required directories exist
$required_dirs = [
    'docs' => 'Documentation directory',
    'docs/bug-fixes' => 'Bug fixes documentation',
    'docs/guidelines' => 'Development guidelines',
    'testing' => 'Testing scripts directory'
];

echo "\nChecking required directories:\n";
foreach ($required_dirs as $dir => $description) {
    $dir_path = $project_root . '/' . $dir;
    if (is_dir($dir_path)) {
        echo "  ✅ $description: $dir/\n";
    } else {
        echo "  ❌ Missing $description: $dir/\n";
        $issues_found++;
    }
}

// Check that testing files are in the right place
echo "\nChecking testing file organization:\n";
$testing_dir = $project_root . '/testing';
if (is_dir($testing_dir)) {
    $testing_files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($testing_dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    $test_file_count = 0;
    foreach ($testing_files as $file) {
        if ($file->isFile() && str_ends_with($file->getFilename(), '.php')) {
            $test_file_count++;
        }
    }
    
    echo "  ✅ Found $test_file_count testing PHP files in testing/ directory\n";
} else {
    echo "  ❌ Testing directory not found\n";
    $issues_found++;
}

// Check documentation organization
echo "\nChecking documentation organization:\n";
$docs_dir = $project_root . '/docs';
if (is_dir($docs_dir)) {
    $doc_files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($docs_dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    $doc_file_count = 0;
    foreach ($doc_files as $file) {
        if ($file->isFile() && str_ends_with($file->getFilename(), '.md')) {
            $doc_file_count++;
        }
    }
    
    echo "  ✅ Found $doc_file_count documentation files in docs/ directory\n";
} else {
    echo "  ❌ Documentation directory not found\n";
    $issues_found++;
}

// Summary
echo "\n" . str_repeat("=", 50) . "\n";
if ($issues_found === 0) {
    echo "🎉 SUCCESS: File organization is compliant!\n";
    echo "All files are properly organized according to guidelines.\n";
} else {
    echo "⚠️  ISSUES FOUND: $issues_found file organization issues detected.\n";
    echo "Please review and fix the issues listed above.\n";
    echo "Refer to docs/guidelines/file-organization.md for guidance.\n";
}

echo "\nFile Organization Guidelines:\n";
echo "- Production code only in: " . implode(', ', $production_dirs) . "\n";
echo "- Testing scripts in: testing/\n";
echo "- Documentation in: docs/\n";
echo "- No mixed content between directories\n";

exit($issues_found > 0 ? 1 : 0);
?>
