version: '3.9'

services:
  db:
    image: mariadb:10.6
    container_name: mariadb
    restart: no
    environment:
      MYSQL_ROOT_PASSWORD: wofaintle!23
      MYSQL_DATABASE: wordpressdb
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wofaintle!23
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - wpsite

  wordpress:
    depends_on:
      - db
    image: wordpress:latest
    container_name: wordpress
    restart: no
    ports:
      - "8000:80"   # 只映射 HTTP 端口
      # 暂时不映射 HTTPS 端口
      # - "8443:443"
    environment:
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wofaintle!23
      WORDPRESS_DB_NAME: wordpressdb
    volumes:
      - ./wordpress:/var/www/html
      - ./plugins:/var/www/html/wp-content/plugins
      - ./themes:/var/www/html/wp-content/themes
      # 暂时不挂载证书和 Apache 配置
      # - ./certs:/etc/apache2/certs
      # - ./apache2/sites-available/default-ssl.conf:/etc/apache2/sites-available/default-ssl.conf
      # 暂时不使用初始化脚本作为 command
      # - ./init.sh:/usr/local/bin/init.sh
    # 暂时不修改 command
    # command: ["/usr/local/bin/init.sh"]
    networks:
      - wpsite

networks:
  wpsite:

volumes:
  db_data:
