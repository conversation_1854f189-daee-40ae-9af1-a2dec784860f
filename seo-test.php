<?php
/**
 * SEO Implementation Test Page
 * 
 * This file tests the Phase 1 SEO implementations
 * Run this in WordPress admin or via WP-CLI to test SEO features
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test SEO Meta Tags Implementation
 */
function test_seo_meta_tags() {
    echo "<h2>🔍 Testing SEO Meta Tags Implementation</h2>\n";
    
    // Test if functions exist
    $functions_to_test = [
        'blogzee_add_meta_tags',
        'blogzee_add_social_meta_tags', 
        'blogzee_add_canonical_urls',
        'blogzee_add_webp_support',
        'blogzee_add_lazy_loading',
        'blogzee_add_security_headers',
        'blogzee_seo_plugin_compatibility'
    ];
    
    foreach ($functions_to_test as $function) {
        if (function_exists($function)) {
            echo "✅ Function {$function} exists\n";
        } else {
            echo "❌ Function {$function} missing\n";
        }
    }
    
    // Test WebP MIME type support
    $allowed_mimes = get_allowed_mime_types();
    if (isset($allowed_mimes['webp'])) {
        echo "✅ WebP MIME type support enabled\n";
    } else {
        echo "❌ WebP MIME type support missing\n";
    }
    
    // Test WordPress hooks
    $hooks_to_test = [
        'wp_head' => ['blogzee_add_meta_tags', 'blogzee_add_social_meta_tags', 'blogzee_add_canonical_urls'],
        'the_content' => ['blogzee_add_lazy_loading'],
        'upload_mimes' => ['blogzee_add_webp_support'],
        'send_headers' => ['blogzee_add_security_headers']
    ];
    
    foreach ($hooks_to_test as $hook => $functions) {
        foreach ($functions as $function) {
            if (has_action($hook, $function)) {
                echo "✅ Hook {$hook} -> {$function} registered\n";
            } else {
                echo "❌ Hook {$hook} -> {$function} not registered\n";
            }
        }
    }
}

/**
 * Test SEO Plugin Compatibility
 */
function test_seo_plugin_compatibility() {
    echo "<h2>🔌 Testing SEO Plugin Compatibility</h2>\n";
    
    $seo_plugins = [
        'WPSEO_VERSION' => 'Yoast SEO',
        'RANK_MATH_VERSION' => 'RankMath',
        'AIOSEO_VERSION' => 'All in One SEO',
        'SEOPRESS_VERSION' => 'SEOPress'
    ];
    
    $active_plugins = [];
    foreach ($seo_plugins as $constant => $plugin_name) {
        if (defined($constant)) {
            $active_plugins[] = $plugin_name;
            echo "✅ {$plugin_name} detected (version: " . constant($constant) . ")\n";
        }
    }
    
    if (empty($active_plugins)) {
        echo "ℹ️ No SEO plugins detected - theme SEO functions will be active\n";
    } else {
        echo "ℹ️ SEO plugins detected - theme SEO functions should be disabled for: " . implode(', ', $active_plugins) . "\n";
    }
}

/**
 * Test Image Optimization Features
 */
function test_image_optimization() {
    echo "<h2>🖼️ Testing Image Optimization Features</h2>\n";
    
    // Test WebP support
    if (function_exists('imagewebp')) {
        echo "✅ Server supports WebP image generation\n";
    } else {
        echo "⚠️ Server does not support WebP image generation\n";
    }
    
    // Test custom image sizes
    global $_wp_additional_image_sizes;
    $custom_sizes = [
        'blogzee-featured-large',
        'blogzee-featured-medium', 
        'blogzee-featured-small',
        'blogzee-thumbnail-webp'
    ];
    
    foreach ($custom_sizes as $size) {
        if (isset($_wp_additional_image_sizes[$size])) {
            $size_info = $_wp_additional_image_sizes[$size];
            echo "✅ Custom image size '{$size}': {$size_info['width']}x{$size_info['height']}\n";
        } else {
            echo "❌ Custom image size '{$size}' not registered\n";
        }
    }
}

/**
 * Test Security Headers
 */
function test_security_headers() {
    echo "<h2>🔒 Testing Security Headers</h2>\n";
    
    // Note: Headers can only be tested when actually serving a page
    // This is a basic check for the function existence
    if (function_exists('blogzee_add_security_headers')) {
        echo "✅ Security headers function exists\n";
        echo "ℹ️ Security headers will be added when serving pages (X-Content-Type-Options, X-Frame-Options, etc.)\n";
    } else {
        echo "❌ Security headers function missing\n";
    }
    
    // Test if unnecessary WordPress head elements are removed
    $removed_actions = [
        'rsd_link',
        'wlwmanifest_link', 
        'wp_shortlink_wp_head',
        'adjacent_posts_rel_link_wp_head'
    ];
    
    foreach ($removed_actions as $action) {
        if (!has_action('wp_head', $action)) {
            echo "✅ Unnecessary action '{$action}' removed from wp_head\n";
        } else {
            echo "⚠️ Action '{$action}' still present in wp_head\n";
        }
    }
}

/**
 * Generate SEO Test Report
 */
function generate_seo_test_report() {
    echo "<h1>📊 SEO Implementation Test Report - Phase 1</h1>\n";
    echo "<p>Testing Date: " . date('Y-m-d H:i:s') . "</p>\n";
    echo "<p>WordPress Version: " . get_bloginfo('version') . "</p>\n";
    echo "<p>Theme: " . get_template() . "</p>\n";
    echo "<hr>\n";
    
    test_seo_meta_tags();
    echo "<hr>\n";
    
    test_seo_plugin_compatibility();
    echo "<hr>\n";
    
    test_image_optimization();
    echo "<hr>\n";
    
    test_security_headers();
    echo "<hr>\n";
    
    echo "<h2>📋 Next Steps</h2>\n";
    echo "<ul>\n";
    echo "<li>✅ Phase 1 (Critical) - Meta tags, Open Graph, WebP, Canonical URLs, Security headers</li>\n";
    echo "<li>⏳ Phase 2 (Important) - JSON-LD structured data, CSS/JS optimization, caching</li>\n";
    echo "<li>⏳ Phase 3 (Enhancement) - XML sitemap, robots.txt, performance monitoring</li>\n";
    echo "</ul>\n";
    
    echo "<h2>🎯 Performance Targets</h2>\n";
    echo "<ul>\n";
    echo "<li>Page load time: < 3 seconds (target: 2 seconds)</li>\n";
    echo "<li>Google Lighthouse Performance Score: 90+ (target: 95+)</li>\n";
    echo "<li>First Contentful Paint (FCP): < 1.8 seconds</li>\n";
    echo "<li>Largest Contentful Paint (LCP): < 2.5 seconds</li>\n";
    echo "<li>Cumulative Layout Shift (CLS): < 0.1</li>\n";
    echo "</ul>\n";
}

// Run the test if accessed directly or via admin
if (is_admin() || (defined('WP_CLI') && WP_CLI)) {
    generate_seo_test_report();
}

/**
 * Add admin menu for SEO testing
 */
function add_seo_test_admin_menu() {
    add_management_page(
        'SEO Test Report',
        'SEO Test',
        'manage_options',
        'seo-test-report',
        'display_seo_test_page'
    );
}
add_action('admin_menu', 'add_seo_test_admin_menu');

/**
 * Display SEO test page in admin
 */
function display_seo_test_page() {
    echo '<div class="wrap">';
    echo '<style>h1,h2{color:#23282d;} ul{margin-left:20px;} .wrap{font-family:monospace;}</style>';
    generate_seo_test_report();
    echo '</div>';
}
