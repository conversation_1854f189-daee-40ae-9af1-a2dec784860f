# WordPress Price Comparison Site

This is a WordPress-based price comparison website built with Docker.

## Project Structure

```
wp-pricecomp/
├── docs/                        # Documentation and reports
│   ├── bug-fixes/              # Bug fix documentation
│   └── guidelines/             # Development guidelines
├── testing/                     # Testing scripts and verification tools
│   └── vote-system/            # Vote system testing scripts
├── themes/                      # WordPress themes (production only)
├── plugins/                     # WordPress plugins (production only)
├── wordpress/                   # WordPress core files
└── [config files]              # Docker and server configuration
```

## Documentation

All project documentation is organized in the `docs/` directory:
- **Bug Fixes**: Historical record of issues and their resolutions
- **Guidelines**: Development standards and best practices
- **Specifications**: Technical documentation and API specs

## Testing

Testing scripts and verification tools are located in the `testing/` directory:
- **Feature Tests**: Comprehensive testing scripts for specific features
- **Verification Scripts**: Simple validation tools for bug fixes
- **Integration Tests**: Cross-feature testing utilities

See `testing/README.md` for detailed testing information.

## File Organization

This project follows strict file organization guidelines:
- **Production Code**: Only in main directories (`themes/`, `plugins/`, `wordpress/`)
- **Testing Code**: Isolated in `testing/` directory
- **Documentation**: Centralized in `docs/` directory
- **No Mixed Content**: Testing and documentation files are never mixed with production code

For detailed guidelines, see `docs/guidelines/file-organization.md`.

## Development Workflow

1. **Bug Fixes**: Document in `docs/bug-fixes/`, create tests in `testing/`
2. **New Features**: Specify in `docs/specifications/`, test in `testing/`
3. **File Organization**: Always follow the established directory structure
4. **Clean Production**: Keep main directories free of testing/debug files

## Getting Started

1. Clone the repository
2. Review documentation in `docs/`
3. Set up development environment
4. Follow file organization guidelines for any new development work