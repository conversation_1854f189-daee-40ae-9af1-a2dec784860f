version: '3.9'

services:
  db:
    image: mariadb:10.6
    container_name: mariadb
    restart: no
    environment:
      MYSQL_ROOT_PASSWORD: wofaintle!23
      MYSQL_DATABASE: wordpressdb
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wofaintle!23
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - wpsite

  wordpress:
    depends_on:
      - db
    image: wordpress:latest
    container_name: wordpress
    restart: no
    ports:
      - "8000:80"
      - "8443:443"
    environment:
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wofaintle!23
      WORDPRESS_DB_NAME: wordpressdb
    volumes:
      - ./wordpress:/var/www/html
      - ./plugins:/var/www/html/wp-content/plugins
      - ./themes:/var/www/html/wp-content/themes
      - ./certs:/etc/apache2/certs
      - ./apache2/sites-available/default-ssl.conf:/etc/apache2/sites-available/default-ssl.conf
      # 移除 mods-available 和 mods-enabled 的挂载，让 init.sh 来处理模块启用
      # - ./apache2/mods-available/ssl.conf:/etc/apache2/mods-available/ssl.conf
      # - ./apache2/mods-enabled/ssl.load:/etc/apache2/mods-enabled/ssl.load
      # 新增：挂载初始化脚本
      - ./init.sh:/usr/local/bin/init.sh
    # 新增：执行初始化脚本
    command: ["/usr/local/bin/init.sh"]
    networks:
      - wpsite

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: phpmyadmin
    restart: no
    ports:
      - "8080:80"
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      MYSQL_ROOT_PASSWORD: wofaintle!23
    networks:
      - wpsite

networks:
  wpsite:

volumes:
  db_data:
