# File Organization Guidelines

This document establishes standard practices for organizing files during development work, particularly for testing, verification, and documentation files.

## Core Principles

### 1. Separation of Concerns
- **Production Code**: Keep only production-ready code in main directories
- **Testing Code**: Isolate all testing scripts and verification tools
- **Documentation**: Centralize all documentation and reports
- **Temporary Files**: Never commit temporary or debug files to production directories

### 2. Clean Production Directories
Main source code directories should contain only:
- ✅ Production-ready source code
- ✅ Configuration files required for operation
- ✅ Essential README files
- ❌ Testing scripts
- ❌ Debug files
- ❌ Verification tools
- ❌ Bug fix documentation

## Directory Structure Standards

### Project Root Structure
```
project-root/
├── docs/                        # All documentation
│   ├── README.md               # Documentation index
│   ├── bug-fixes/              # Bug fix reports
│   ├── guidelines/             # Development guidelines
│   └── specifications/         # Technical specifications
├── testing/                     # All testing code
│   ├── README.md               # Testing guide
│   ├── [feature-name]/         # Feature-specific tests
│   ├── integration/            # Integration tests
│   └── utilities/              # Testing utilities
├── themes/                      # WordPress themes (production only)
├── plugins/                     # WordPress plugins (production only)
├── wordpress/                   # WordPress core (production only)
└── [other-production-dirs]/     # Other production directories
```

### Documentation Directory (`docs/`)
```
docs/
├── README.md                    # Documentation index
├── bug-fixes/                   # Bug fix reports and analysis
│   ├── [YYYY-MM-DD]-[issue-name].md
│   └── [bug-fix-report].md
├── guidelines/                  # Development standards
│   ├── file-organization.md    # This file
│   ├── coding-standards.md     # Code style guidelines
│   └── testing-standards.md    # Testing requirements
├── specifications/              # Technical specifications
│   ├── api-documentation.md    # API specs
│   └── feature-specifications.md
└── deployment/                  # Deployment guides
    ├── setup-instructions.md   # Setup procedures
    └── troubleshooting.md      # Common issues
```

### Testing Directory (`testing/`)
```
testing/
├── README.md                    # Testing overview
├── [feature-name]/              # Feature-specific testing
│   ├── test-[feature].php      # Comprehensive test script
│   ├── verify-[feature].php    # Simple verification script
│   └── README.md               # Feature testing guide
├── integration/                 # Integration tests
│   ├── api-tests/              # API integration tests
│   └── database-tests/         # Database integration tests
├── utilities/                   # Testing utilities
│   ├── test-helpers.php        # Common testing functions
│   └── mock-data.php           # Test data generators
└── reports/                     # Test execution reports
    ├── [YYYY-MM-DD]-test-results.md
    └── coverage-reports/       # Code coverage reports
```

## File Naming Conventions

### Documentation Files
- **Bug Fix Reports**: `[YYYY-MM-DD]-[brief-description].md`
- **Guidelines**: `[topic]-guidelines.md` or `[topic]-standards.md`
- **Specifications**: `[feature]-specification.md`
- **README Files**: Always `README.md` (uppercase)

### Testing Files
- **Test Scripts**: `test-[feature-name].php`
- **Verification Scripts**: `verify-[feature-name].php`
- **Utility Scripts**: `[utility-name]-helper.php`
- **Mock Data**: `mock-[data-type].php`

### Production Files
- Follow existing project conventions
- Use descriptive, consistent naming
- Avoid temporary or debug suffixes

## Development Workflow

### During Bug Fixes
1. **Create Issue**: Document the problem in `docs/bug-fixes/`
2. **Develop Fix**: Make changes only to production code
3. **Create Tests**: Add verification scripts to `testing/[feature]/`
4. **Document Solution**: Update bug fix report with resolution
5. **Clean Up**: Ensure no temporary files remain in production directories

### During Feature Development
1. **Plan**: Create specification in `docs/specifications/`
2. **Develop**: Implement in appropriate production directories
3. **Test**: Create comprehensive tests in `testing/[feature]/`
4. **Document**: Add usage documentation to `docs/`
5. **Review**: Ensure file organization follows these guidelines

### File Movement Checklist
When organizing files after development:

- [ ] Move all testing scripts to `testing/[feature]/`
- [ ] Move all documentation to `docs/[category]/`
- [ ] Update file paths in moved scripts
- [ ] Create README files for new directories
- [ ] Remove temporary files from production directories
- [ ] Update any references to moved files
- [ ] Test that moved scripts still function correctly

## Best Practices

### DO
- ✅ Create dedicated subdirectories for each feature's tests
- ✅ Include both comprehensive and simple verification scripts
- ✅ Document the purpose and usage of each testing script
- ✅ Use consistent naming conventions
- ✅ Update README files when adding new directories
- ✅ Include proper error handling in all scripts
- ✅ Add security checks (admin privileges, nonces) to testing scripts

### DON'T
- ❌ Leave testing files in production directories
- ❌ Commit temporary or debug files
- ❌ Mix documentation with source code
- ❌ Use unclear or inconsistent file names
- ❌ Create deeply nested directory structures
- ❌ Forget to update file paths after moving files
- ❌ Skip documentation for new testing utilities

## Maintenance

### Regular Cleanup
- Review production directories monthly for misplaced files
- Archive old testing scripts that are no longer relevant
- Update documentation when file structures change
- Ensure README files remain current and accurate

### Version Control
- Use `.gitignore` to exclude temporary files
- Commit documentation and testing files alongside production code
- Use meaningful commit messages when reorganizing files
- Tag releases to maintain historical organization snapshots

## Compliance

All team members should:
1. Follow these guidelines for new development work
2. Reorganize existing files to comply with these standards
3. Update this document when new patterns emerge
4. Review file organization during code reviews

## Examples

### Good Organization
```
✅ themes/blogzee/functions.php              (production code)
✅ testing/vote-system/test-vote-fix.php     (testing script)
✅ docs/bug-fixes/vote-counter-fix.md        (documentation)
```

### Poor Organization
```
❌ themes/blogzee/test-vote-fix.php          (testing in production)
❌ themes/blogzee/debug-votes.php            (debug file in production)
❌ themes/blogzee/VOTE_BUG_FIX_REPORT.md    (docs in production)
```

---

**Last Updated**: 2025-06-19
**Version**: 1.0
**Status**: Active Standard
