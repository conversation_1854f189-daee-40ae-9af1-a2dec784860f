# Documentation Directory

This directory contains all project documentation, including bug fix reports, technical specifications, and development guides.

## Directory Structure

```
docs/
├── README.md                    # This file
├── bug-fixes/                   # Bug fix reports and documentation
│   └── VOTE_BUG_FIX_REPORT.md  # Like/Dislike counter bug fix report
└── guidelines/                  # Development guidelines and standards
    └── file-organization.md     # File organization standards
```

## Bug Fixes

### Vote System Bug Fix (2025-06-19)
- **File**: `bug-fixes/VOTE_BUG_FIX_REPORT.md`
- **Issue**: Like/Dislike counter showing incorrect values
- **Status**: ✅ Fixed
- **Impact**: WordPress theme Blogzee voting functionality

## Guidelines

All development guidelines and coding standards are documented in the `guidelines/` subdirectory.

## Usage

- **For Developers**: Reference these documents when working on related features
- **For QA**: Use bug fix reports to understand testing scenarios
- **For Maintenance**: Historical record of issues and their resolutions

## File Organization Standards

All documentation files should be organized according to the project's file organization guidelines found in `guidelines/file-organization.md`.
