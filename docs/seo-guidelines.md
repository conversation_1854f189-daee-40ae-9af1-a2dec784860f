# SEO Guidelines for WordPress Theme Development

## Overview
This document outlines comprehensive SEO requirements that must be followed during all code development for this WordPress theme project. These guidelines ensure optimal search engine visibility, performance, and user experience.

## 1. Performance & Loading Speed

### Target Metrics
- **Page load time**: Under 3 seconds (target: 2 seconds)
- **Google Lighthouse Performance Score**: 90+ (target: 95+)
- **First Contentful Paint (FCP)**: Under 1.8 seconds
- **Largest Contentful Paint (LCP)**: Under 2.5 seconds
- **Cumulative Layout Shift (CLS)**: Under 0.1

### Image Optimization
```php
// Implement WebP support in functions.php
function blogzee_add_webp_support($mimes) {
    $mimes['webp'] = 'image/webp';
    return $mimes;
}
add_filter('upload_mimes', 'blogzee_add_webp_support');

// Add lazy loading attributes
function blogzee_add_lazy_loading($content) {
    return str_replace('<img', '<img loading="lazy"', $content);
}
add_filter('the_content', 'blogzee_add_lazy_loading');
```

### CSS/JS Optimization
```php
// Minify and combine CSS/JS files
function blogzee_optimize_assets() {
    if (!is_admin()) {
        // Remove unnecessary scripts
        wp_dequeue_script('wp-embed');
        
        // Defer non-critical JavaScript
        add_filter('script_loader_tag', 'blogzee_defer_scripts', 10, 3);
    }
}
add_action('wp_enqueue_scripts', 'blogzee_optimize_assets');

function blogzee_defer_scripts($tag, $handle, $src) {
    $defer_scripts = ['blogzee-main-js', 'blogzee-navigation'];
    if (in_array($handle, $defer_scripts)) {
        return str_replace(' src', ' defer src', $tag);
    }
    return $tag;
}
```

### Caching Implementation
```php
// Add cache headers
function blogzee_add_cache_headers() {
    if (!is_admin()) {
        header('Cache-Control: public, max-age=31536000');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
    }
}
add_action('send_headers', 'blogzee_add_cache_headers');
```

## 2. Site Structure & Navigation

### URL Structure Requirements
- Use descriptive, keyword-rich URLs
- Implement proper permalink structure: `/%category%/%postname%/`
- Avoid deep nesting (max 3 levels)
- Use hyphens for word separation

### Semantic HTML5 Structure
```html
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>
    <header id="masthead" class="site-header">
        <nav id="site-navigation" class="main-navigation" role="navigation" aria-label="<?php esc_attr_e('Primary Menu', 'blogzee'); ?>">
            <!-- Navigation content -->
        </nav>
    </header>
    
    <main id="primary" class="site-main">
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <!-- Article content -->
        </article>
    </main>
    
    <aside id="secondary" class="widget-area" role="complementary">
        <!-- Sidebar content -->
    </aside>
    
    <footer id="colophon" class="site-footer">
        <!-- Footer content -->
    </footer>
</body>
</html>
```

### Breadcrumb Implementation
```php
function blogzee_breadcrumbs() {
    if (!is_home()) {
        echo '<nav class="breadcrumbs" aria-label="Breadcrumb">';
        echo '<ol itemscope itemtype="https://schema.org/BreadcrumbList">';
        
        // Home link
        echo '<li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">';
        echo '<a itemprop="item" href="' . home_url() . '"><span itemprop="name">Home</span></a>';
        echo '<meta itemprop="position" content="1" />';
        echo '</li>';
        
        // Additional breadcrumb logic here
        echo '</ol>';
        echo '</nav>';
    }
}
```

## 3. Content Optimization

### Heading Hierarchy
```php
// Ensure proper heading structure in templates
function blogzee_validate_heading_structure($content) {
    // Only one H1 per page (handled by theme structure)
    // H2-H6 should follow logical hierarchy
    return $content;
}

// Template example
?>
<article>
    <h1 class="entry-title"><?php the_title(); ?></h1> <!-- Only one H1 -->
    <div class="entry-content">
        <h2>Section Title</h2>
        <h3>Subsection Title</h3>
        <!-- Content hierarchy -->
    </div>
</article>
```

### Meta Tags Implementation
```php
function blogzee_add_meta_tags() {
    if (is_singular()) {
        global $post;
        
        // Title tag (50-60 characters)
        $title = get_the_title();
        if (strlen($title) > 60) {
            $title = substr($title, 0, 57) . '...';
        }
        
        // Meta description (150-160 characters)
        $description = get_the_excerpt();
        if (strlen($description) > 160) {
            $description = substr($description, 0, 157) . '...';
        }
        
        echo '<meta name="description" content="' . esc_attr($description) . '">';
    }
}
add_action('wp_head', 'blogzee_add_meta_tags');
```

### Image Alt Text Requirements
```php
// Ensure all images have descriptive alt text
function blogzee_validate_image_alt($content) {
    // Check for images without alt attributes
    if (preg_match_all('/<img[^>]+>/i', $content, $matches)) {
        foreach ($matches[0] as $img) {
            if (!preg_match('/alt=["\'][^"\']*["\']/', $img)) {
                // Log warning or add default alt text
                error_log('Image missing alt text: ' . $img);
            }
        }
    }
    return $content;
}
add_filter('the_content', 'blogzee_validate_image_alt');
```

## 4. Technical SEO

### Mobile-First Responsive Design
```css
/* Mobile-first approach */
.container {
    width: 100%;
    padding: 0 15px;
}

/* Tablet */
@media (min-width: 768px) {
    .container {
        max-width: 750px;
        margin: 0 auto;
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .container {
        max-width: 1200px;
    }
}
```

### Canonical URLs
```php
function blogzee_add_canonical_url() {
    if (is_singular()) {
        echo '<link rel="canonical" href="' . get_permalink() . '">';
    } elseif (is_home() || is_front_page()) {
        echo '<link rel="canonical" href="' . home_url() . '">';
    }
}
add_action('wp_head', 'blogzee_add_canonical_url');
```

### Open Graph & Twitter Cards
```php
function blogzee_add_social_meta_tags() {
    if (is_singular()) {
        global $post;
        
        // Open Graph
        echo '<meta property="og:title" content="' . get_the_title() . '">';
        echo '<meta property="og:description" content="' . get_the_excerpt() . '">';
        echo '<meta property="og:url" content="' . get_permalink() . '">';
        echo '<meta property="og:type" content="article">';
        
        if (has_post_thumbnail()) {
            $image = wp_get_attachment_image_src(get_post_thumbnail_id(), 'large');
            echo '<meta property="og:image" content="' . $image[0] . '">';
        }
        
        // Twitter Cards
        echo '<meta name="twitter:card" content="summary_large_image">';
        echo '<meta name="twitter:title" content="' . get_the_title() . '">';
        echo '<meta name="twitter:description" content="' . get_the_excerpt() . '">';
    }
}
add_action('wp_head', 'blogzee_add_social_meta_tags');
```

## 5. WordPress-Specific Requirements

### SEO Plugin Compatibility
```php
// Ensure compatibility with Yoast SEO
function blogzee_yoast_compatibility() {
    // Remove theme's meta description if Yoast is active
    if (defined('WPSEO_VERSION')) {
        remove_action('wp_head', 'blogzee_add_meta_tags');
    }
}
add_action('init', 'blogzee_yoast_compatibility');

// Add hooks for SEO customization
function blogzee_seo_hooks() {
    do_action('blogzee_before_head_close');
    do_action('blogzee_after_body_open');
    do_action('blogzee_before_body_close');
}
```

### Database Query Optimization
```php
// Optimize queries to prevent slow loading
function blogzee_optimize_queries($query) {
    if (!is_admin() && $query->is_main_query()) {
        if (is_home()) {
            $query->set('posts_per_page', 10);
            $query->set('meta_query', array(
                'relation' => 'OR',
                array(
                    'key' => '_thumbnail_id',
                    'compare' => 'EXISTS'
                ),
                array(
                    'key' => '_thumbnail_id',
                    'compare' => 'NOT EXISTS'
                )
            ));
        }
    }
}
add_action('pre_get_posts', 'blogzee_optimize_queries');
```

## 6. Structured Data & Schema Markup

### Article Schema Implementation
```php
function blogzee_add_article_schema() {
    if (is_singular('post')) {
        global $post;

        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title(),
            'description' => get_the_excerpt(),
            'image' => wp_get_attachment_image_src(get_post_thumbnail_id(), 'large')[0],
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author()
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => get_theme_file_uri('/assets/images/logo.png')
                )
            ),
            'datePublished' => get_the_date('c'),
            'dateModified' => get_the_modified_date('c'),
            'mainEntityOfPage' => get_permalink()
        );

        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>';
    }
}
add_action('wp_head', 'blogzee_add_article_schema');
```

### Breadcrumb Schema
```php
function blogzee_breadcrumb_schema() {
    if (!is_home()) {
        $items = array();
        $position = 1;

        // Home
        $items[] = array(
            '@type' => 'ListItem',
            'position' => $position++,
            'name' => 'Home',
            'item' => home_url()
        );

        // Category/Archive pages
        if (is_category()) {
            $category = get_queried_object();
            $items[] = array(
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => $category->name,
                'item' => get_category_link($category->term_id)
            );
        }

        // Current page
        if (is_singular()) {
            $items[] = array(
                '@type' => 'ListItem',
                'position' => $position,
                'name' => get_the_title(),
                'item' => get_permalink()
            );
        }

        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $items
        );

        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>';
    }
}
add_action('wp_head', 'blogzee_breadcrumb_schema');
```

## 7. Security & HTTPS Implementation

### Security Headers
```php
function blogzee_add_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
    }
}
add_action('send_headers', 'blogzee_add_security_headers');
```

### HTTPS Enforcement
```php
function blogzee_force_https() {
    if (!is_ssl() && !is_admin()) {
        wp_redirect('https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'], 301);
        exit();
    }
}
add_action('template_redirect', 'blogzee_force_https');
```

## 8. XML Sitemap & Robots.txt

### Custom Sitemap Generation
```php
function blogzee_generate_sitemap() {
    if (get_query_var('sitemap')) {
        header('Content-Type: application/xml; charset=utf-8');

        echo '<?xml version="1.0" encoding="UTF-8"?>';
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';

        // Homepage
        echo '<url>';
        echo '<loc>' . home_url() . '</loc>';
        echo '<changefreq>daily</changefreq>';
        echo '<priority>1.0</priority>';
        echo '</url>';

        // Posts
        $posts = get_posts(array('numberposts' => -1, 'post_status' => 'publish'));
        foreach ($posts as $post) {
            echo '<url>';
            echo '<loc>' . get_permalink($post->ID) . '</loc>';
            echo '<lastmod>' . get_the_modified_date('c', $post->ID) . '</lastmod>';
            echo '<changefreq>weekly</changefreq>';
            echo '<priority>0.8</priority>';
            echo '</url>';
        }

        echo '</urlset>';
        exit;
    }
}
add_action('template_redirect', 'blogzee_generate_sitemap');

function blogzee_add_sitemap_rewrite() {
    add_rewrite_rule('^sitemap\.xml$', 'index.php?sitemap=1', 'top');
}
add_action('init', 'blogzee_add_sitemap_rewrite');
```

### Robots.txt Optimization
```php
function blogzee_custom_robots_txt($output) {
    $output .= "User-agent: *\n";
    $output .= "Disallow: /wp-admin/\n";
    $output .= "Disallow: /wp-includes/\n";
    $output .= "Disallow: /wp-content/plugins/\n";
    $output .= "Disallow: /wp-content/themes/\n";
    $output .= "Allow: /wp-content/uploads/\n";
    $output .= "Sitemap: " . home_url('/sitemap.xml') . "\n";

    return $output;
}
add_filter('robots_txt', 'blogzee_custom_robots_txt');
```

## 9. Performance Monitoring & Analytics

### Core Web Vitals Tracking
```javascript
// Add to theme's main JavaScript file
function trackWebVitals() {
    // Track Largest Contentful Paint (LCP)
    new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
            if (entry.startTime < 2500) {
                // Good LCP
                gtag('event', 'web_vitals', {
                    name: 'LCP',
                    value: Math.round(entry.startTime),
                    rating: 'good'
                });
            }
        }
    }).observe({entryTypes: ['largest-contentful-paint']});

    // Track First Input Delay (FID)
    new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
            gtag('event', 'web_vitals', {
                name: 'FID',
                value: Math.round(entry.processingStart - entry.startTime),
                rating: entry.processingStart - entry.startTime < 100 ? 'good' : 'poor'
            });
        }
    }).observe({entryTypes: ['first-input']});
}

// Initialize tracking when DOM is ready
document.addEventListener('DOMContentLoaded', trackWebVitals);
```

### Performance Budget Configuration
```json
{
  "budget": [
    {
      "resourceSizes": [
        {
          "resourceType": "script",
          "budget": 150
        },
        {
          "resourceType": "stylesheet",
          "budget": 50
        },
        {
          "resourceType": "image",
          "budget": 300
        }
      ]
    }
  ]
}
```

## 10. Testing Procedures

### Performance Testing
1. **Google PageSpeed Insights**: Test all page types (home, single post, archive)
   - Target: 90+ score for both mobile and desktop
   - Monitor Core Web Vitals metrics
   - Address all opportunities and diagnostics

2. **GTmetrix**: Monitor loading times and optimization scores
   - Target: Grade A performance
   - Page load time under 3 seconds
   - Total page size under 2MB

3. **WebPageTest**: Analyze waterfall charts and identify bottlenecks
   - First Byte Time under 200ms
   - Start Render under 1.5 seconds
   - Speed Index under 2.5 seconds

4. **Lighthouse CI**: Integrate automated testing in development workflow
   ```bash
   npm install -g @lhci/cli
   lhci autorun --upload.target=temporary-public-storage
   ```

### SEO Testing Checklist
- [ ] Validate HTML markup (W3C Validator)
- [ ] Test mobile responsiveness (Google Mobile-Friendly Test)
- [ ] Verify structured data (Google Rich Results Test)
- [ ] Check internal linking structure
- [ ] Validate XML sitemap functionality
- [ ] Test social media sharing (Facebook Debugger, Twitter Card Validator)
- [ ] Verify canonical URLs are properly set
- [ ] Check robots.txt accessibility and content
- [ ] Test page loading speed on 3G networks
- [ ] Validate HTTPS implementation and security headers

### Code Quality Standards
```bash
# Run these commands before deployment
npm run lint:css
npm run lint:js
composer run phpcs
wp theme test blogzee

# Performance testing
lighthouse --output=json --output-path=./lighthouse-report.json https://yoursite.com
```

## 11. Implementation Priority

### Phase 1 (Critical - Week 1)
1. Core Web Vitals optimization
   - Image optimization and lazy loading
   - CSS/JS minification and deferring
   - Critical CSS inlining
2. Mobile responsiveness validation
3. Semantic HTML structure implementation
4. Basic meta tags (title, description)

### Phase 2 (Important - Week 2-3)
1. Structured data implementation
   - Article schema
   - Breadcrumb schema
   - Organization schema
2. Social media meta tags (Open Graph, Twitter Cards)
3. Advanced caching strategies
4. Security headers implementation

### Phase 3 (Enhancement - Week 4+)
1. Advanced schema markup (FAQ, How-to, Review)
2. AMP compatibility
3. Progressive Web App features
4. Advanced analytics integration
5. Internationalization for SEO

## 12. Monitoring & Maintenance

### Regular Audits
- **Weekly**: Monitor Core Web Vitals in Google Search Console
- **Monthly**: Full performance audits using Lighthouse
- **Quarterly**: Comprehensive SEO audits using tools like Screaming Frog
- **Bi-annually**: Security and accessibility audits

### Key Performance Indicators (KPIs)
1. **Technical SEO**:
   - Page load speed (target: <3s)
   - Lighthouse performance score (target: 90+)
   - Mobile usability score (target: 100%)
   - Core Web Vitals passing rate (target: 90%+)

2. **Content SEO**:
   - Average time on page (target: >2 minutes)
   - Bounce rate (target: <60%)
   - Pages per session (target: >2)
   - Organic click-through rate (target: >3%)

### Documentation Updates
- Update guidelines when Google algorithm changes
- Document new SEO features and implementations
- Maintain changelog of SEO-related modifications
- Regular review of industry best practices

## 13. Emergency Response Procedures

### Performance Degradation
1. Identify the cause using performance monitoring tools
2. Implement immediate fixes (disable plugins, optimize images)
3. Communicate with stakeholders about the issue and timeline
4. Document the incident and prevention measures

### SEO Ranking Drops
1. Check Google Search Console for manual actions
2. Analyze recent changes to the website
3. Review competitor activities
4. Implement corrective measures based on findings

---

**IMPORTANT**: These guidelines must be referenced and followed in all future development tasks. Any deviation should be documented and justified with performance/SEO impact analysis. All team members must be familiar with these guidelines and incorporate them into their development workflow.

**Last Updated**: 2025-06-28
**Next Review**: 2025-09-28
